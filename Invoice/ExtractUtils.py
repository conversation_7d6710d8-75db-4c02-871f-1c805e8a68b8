from fuzzywuzzy import fuzz 
from fuzzywuzzy import process
from ml_li_items.mlextractLI.markheaders import get_liheader_fields 
from pyjarowinkler import distance
import os 
#PIL Imports
from PIL import Image
#import Image
import math
import json
#Import Settings for 
from AMS.extract_settings import *
import spacy
from spacy.tokens import Doc

from collections import Counter 
from pyjarowinkler import distance as d
from scipy.spatial import distance as sp_distance
from  AMS.settings import BASE_DIR
import pandas as pd 
from spacy.matcher import Matcher
import csv
import pandas as pd 
from Invoice.DbConfig import *
from Invoice.LineItemUtils import *
import re
from fuzzywuzzy import process






#Custom Tokenizer for tokeizing with whitespace 
class WhitespaceTokenizer(object):
    def __init__(self, vocab):
        self.vocab = vocab

    def __call__(self, text):
        words = text.split(' ')
        # All tokens 'own' a subsequent space character in this tokenizer
        spaces = [True] * len(words)
        return Doc(self.vocab, words=words, spaces=spaces)

class TableUtils:


    def get_rows_columns_map(self,table_result, blocks_map):
        rows = {}
        for relationship in table_result['Relationships']:
            if relationship['Type'] == 'CHILD':
                for child_id in relationship['Ids']:
                    cell = blocks_map[child_id]
                    if cell['BlockType'] == 'CELL':
                        row_index = cell['RowIndex']
                        col_index = cell['ColumnIndex']
                        if row_index not in rows:
                            # create new row
                            rows[row_index] = {}
                            
                        # get the text value
                        rows[row_index][col_index] = self.get_text(cell, blocks_map)
        return rows


   

    def get_table_csv_results(self,filename,jsonname,height):

        
        with open(jsonname) as json_file:
            data=json.load(json_file)
                
        print("File Read "+jsonname)
        
        response=data
        
    
    

        csv_list=[]
        # Get the text blocks
        blocks=response['Blocks']
        #pprint(blocks)

        tables_dict={}
        table_tuple=[]
        blocks_map = {}
        table_blocks = []
        for block in blocks:
            blocks_map[block['Id']] = block
            if block['BlockType'] == "TABLE":
                table_blocks.append(block)
                top_key= int(block['Geometry']['BoundingBox']['Top']*(height/10)) 
                tables_dict[top_key]=block
                table_tuple.append((top_key,block))

        if len(table_blocks) <= 0:
            return None

        #Print length of  Table Blocks
        print("length of table blocks ",len(table_blocks))
        print(" Table keys ",  tables_dict.keys())
        
    
        
        index=0
        for key in table_tuple: #sorted(tables_dict.keys()):
            csv = ''
            
            #blocks= tables_dict[key]
            blocks=key[1]
            #csv += self.generate_table_csv(tables_dict[key], blocks_map, index +1)
            csv += self.generate_table_csv(blocks, blocks_map, index +1)
            top= blocks['Geometry']['BoundingBox']['Top']
            bottom=blocks['Geometry']['BoundingBox']['Top'] +  blocks['Geometry']['BoundingBox']['Height']
            Confidence=blocks["Confidence"]
            csv_list.append( (csv,top,bottom,Confidence) )
            #csv += '\n\n'
            index=index+1
        
        return csv_list
        


    def generate_table_csv(self,table_result, blocks_map, table_index):
        rows = self.get_rows_columns_map(table_result, blocks_map)

        table_id = 'Table_' + str(table_index)
        
        # get cells.
        # csv = 'Table: {0}\n\n'.format(table_id)
        csv=''
        for row_index, cols in rows.items():
            
            for col_index, text in cols.items():
                csv += '{}'.format(text) + ","
            csv += '\n'
            
        csv += '\n\n\n'
        return csv

    def get_text(self,result, blocks_map):
        text = ''
        if 'Relationships' in result:
            for relationship in result['Relationships']:
                if relationship['Type'] == 'CHILD':
                    for child_id in relationship['Ids']:
                        word = blocks_map[child_id]
                        if word['BlockType'] == 'WORD':
                            text += word['Text'] + ' '
                            
                        """     
                        if word['BlockType'] == 'SELECTION_ELEMENT':
                            if word['SelectionStatus'] =='SELECTED':
                                text +=  'X '
                        """
        return text.replace(',','')





    def parse_main(self,filename,jsonname,height,mapping_file):
        table_csv = self.get_table_csv_results(filename,jsonname,height)
        if table_csv is None :
            return None 
        
        cv_path=jsonname.replace('.json','-csv')
        
       
        if not os.path.isdir(cv_path):
            os.mkdir(cv_path)
        
        print("len of table csv ",len(table_csv))
        counter=1
        file_info_dict={}
        print("table_csv ")
        print(table_csv)
        for table,top,bottom,confidence in table_csv:
            filename_=cv_path+"/"+str(counter)+'.csv'
            
            with open(filename_, "wt") as fout:
                fout.write(table)
            counter=counter+1
            file_info_dict[filename_]=[top,bottom,confidence]    
            print('CSV OUTPUT FILE: ', filename_,"top ",top,"bottom",bottom)

        
        
        fout.close()
        #dump length info to file 
        with open( mapping_file, 'w') as fp:
            json.dump(file_info_dict, fp)
    
        return cv_path



def checkDuplicate(item,list_):
    
    for i in list_:
        if item in i:
            return True
    return False

def findLowestScore(items):
    least_score=0
    least_item=[]
    result=[]
    count=0
    
   
    
    for item in items:
        if count==0:
            least_score=item[3]
            least_item.append(item)
        elif item[3]<least_score:
            least_score=item[3]
            least_item.clear()
            least_item.append(item)
            
    return least_item

def intersect(List1, List2):
    # empty list for values that match
    ret = []
    for i in List2:
        for j in List1:
            if i in j:
                ret.append(j)
    return ret


def getcordinates(list1,invoice_arr_dict):
    
    for item in invoice_arr_dict:
        if list1==invoice_arr_dict[item]:
            return item


#method for creating matrix of all invoice text 
#Returns cordinates with row 

def createInvocieArray(data):

    text_cordinates=[] 
    
        
    for blocks in data['Blocks']:
        temp_str=''
                
        a=blocks["Geometry"]["Polygon"][0]['X']
        b=blocks["Geometry"]["Polygon"][0]['Y']
            
        c=blocks["Geometry"]["Polygon"][1]['X']
        d=blocks["Geometry"]["Polygon"][1]['Y']
            #print(blocks["Geometry"]["Polygon"])
            # blocks['Geometry']['BoundingBox']['Top'] 
            # (int)(b*100),(int)(((a+c)/2)*100)
        if (blocks["BlockType"]=='LINE' )  :
            text_cordinates.append((blocks["Text"],math.ceil(b*100), ((a+c)/2)*100,a*100, (c-a)*100,b ))
    
    line_dict={}
    for item in text_cordinates:
        line_no=item[1]
        line_no_a=line_no-1
        if (line_no in line_dict)  :
            temp=line_dict[line_no]
            temp.append(item)
            line_dict[line_no]=temp
        else:
            temp=[]
            temp.append(item)
            line_dict[line_no]=temp
    
    
    all_arr=[]
    invoice_arr_dict={}
    rows=0
    for item in line_dict.keys():
        #print(item)
        cols=0
        temp=[]
        for i in line_dict[item]:
            #print(type(i[0]))
            #invoice_arr[rows][cols]=i[0]
            temp.append(i[0])
            cols=cols+1
            #print(item,i[0])
        rows=rows+1
        all_arr.append(temp)
        #print(line_dict[item][0][5])
        invoice_arr_dict[line_dict[item][0][5]]=temp
    return invoice_arr_dict,all_arr


import re
def intPercent(s):
    r=re.finditer(r'\d+(?:\.\d+)?', s)
    t=len(s)
    numbers=0
    percent=0
    for i in r:
        #print(i.start() ,i.end(), i.end()-i.start()-1)
        numbers=numbers + (i.end()-i.start())

    if t >0:
        percent=(numbers/t)*100 
    
    return percent
        
        
    





#utilities used for Preprocessing and Extraction of Data 
class ExtractUtils:
    #Wifth and height of Image
    width=0
    height=0
    #Dimesion of Tables in Invoice
    top=0
    bottom=0
    #Footer Lists of Invoice
    footer_list=[]
    footer_blocks={}

    #Stopwords
    stopwords={'$','ourselves', 'hers', 'between', 'yourself', 'but', 'again', 'there', 'about', 'once', 'during', 'out', 'very', 'having', 'with', 'they', 'own', 'an', 'be', 'some', 'for', 'do', 'its', 'yours', 'such', 'into', 'of', 'most', 'itself', 'other', 'off', 'is', 's', 'am', 'or', 'who', 'as', 'from', 'him', 'each', 'the', 'themselves', 'until', 'below', 'are', 'we', 'these', 'your', 'his', 'through', 'don', 'nor', 'me', 'were', 'her', 'more', 'himself', 'this', 'down', 'should', 'our', 'their', 'while', 'above', 'both', 'up', 'to', 'ours', 'had', 'she', 'all','when', 'at', 'any', 'before', 'them', 'same', 'and', 'been', 'have', 'in', 'will', 'on', 'does', 'yourselves', 'then', 'that', 'because', 'what', 'over', 'why', 'so', 'can', 'did', 'not', 'now', 'under', 'he', 'you', 'herself', 'has', 'just', 'where', 'too', 'only', 'myself', 'which', 'those', 'i', 'after', 'few', 'whom', 't', 'being', 'if', 'theirs', 'my', 'against', 'a', 'by', 'doing', 'it', 'how', 'further', 'was', 'here', 'than'}




    #fetches Height if the Image
    def getHeight(self,image_path):

        im = Image.open(image_path)
        width, height = im.size
        result={}
        result["width"]=width
        result["height"]=height
        self.width=width
        self.height=height
        return result

    #Returns Above and Below of Tables 
    def getTableDimensions(self,json_path):
        
        counter=0
        top=0
        bottom=0
        with open(json_path) as json_file:
            data=json.load(json_file)
            table_blocks = []
            final_block=None
            for blocks in data['Blocks']:
                if blocks["BlockType"]=='TABLE':
                    print("table found ....")
                    #print(blocks)
                    #print(".......")

                
                    


                      
          

        
        print("top",top,"bottom",bottom)
        result={}
        result["top"]=top
        result["bottom"]=bottom
        self.top=top
        self.bottom=bottom
        return result


    def createFooterLists(self,json_path,height,bottom):
        
        footer_blocks={}

        with open(json_path) as json_file:
            data=json.load(json_file)
            for blocks in data['Blocks']:
                temp_str=''
                if blocks["BlockType"]=='LINE' and blocks['Geometry']['BoundingBox']['Top']>bottom:
                    #print(blocks)
                    top_key= int(blocks['Geometry']['BoundingBox']['Top']*(height/10)) 

                    if (top_key in footer_blocks):
                        temp_list=footer_blocks[top_key]
                    elif ((top_key-1) in footer_blocks):
                        temp_list=footer_blocks[top_key-1]
                    else:
                        temp_list=[]

                    temp_list.append(blocks)
                    footer_blocks[top_key]= temp_list
                

        footer_list=[]
        for key,value in footer_blocks.items():

            temp_str=""
            for item in value:
                temp_str=temp_str+' '+item['Text']

            footer_list.append(temp_str) 

        self.footer_list = list(dict.fromkeys(footer_list))
        
        return footer_list  
    
    #Remove Stop words from Invoices

    def clean_stop_words(self,header_list,nlp):
        clean_list=[]
        for item in header_list:
            doc = nlp(item.strip())
            temp_str=""
            for token in doc:
                if not token.text.lower() in self.stopwords:
                
                    temp_str=temp_str+" "+token.text
            clean_list.append(temp_str)
        
        return clean_list 
    
    #Creating Invoice Text for Extracting Invoice  Items 
    def createText(self,json_path,height,width,top,bottom):
        text_cordinates=[]
        matches=[]

        with open(json_path) as json_file:
            data=json.load(json_file)
            for blocks in data['Blocks']:
                temp_str=''
                #top_key= int(blocks['Geometry']['BoundingBox']['Top']*(height)/10) 
                #Left_key= int(blocks['Geometry']['BoundingBox']['Top']*(height)/10)
                    
                a=blocks["Geometry"]["Polygon"][0]['X']
                b=blocks["Geometry"]["Polygon"][0]['Y']

                c=blocks["Geometry"]["Polygon"][1]['X']
                d=blocks["Geometry"]["Polygon"][1]['Y']
                    
                if (blocks["BlockType"]=='LINE' and blocks['Geometry']['BoundingBox']['Top']<top)  :
                    text_cordinates.append((blocks["Text"],a,b,c,d))
                
        count=0  

        for item in text_cordinates:
            sub_list=text_cordinates[count+1:count+20]
            temp_matches=[]
            distance_counter=[]
            count2=0

            for item2 in sub_list:
        
                a=(item[1],item[2])
                b=(item2[1],item2[2])
                dst = sp_distance.euclidean(a, b)
                myradians = math.atan2(b[1]-a[1], b[0]-a[0])
                degrees = math.degrees(myradians)
                degrees=int(degrees)
                #print(degrees,' -- ',item[0],' -- ',item2[0],' -- ',dst)
                if degrees>-4 and degrees<4 and dst<0.39:
                    temp_matches.append( (item[0],item2[0],degrees,dst) )
        
                

            temp_matches=findLowestScore(temp_matches) 

            if len(temp_matches)==0:
                matches.append((item[0]))
            else:
                for i in temp_matches:
                    #print("item",i)
                    matches.append(i)
            count+=1
     
        header_list=[]
        count_=0
        for item in matches:
            #print(str(type))
            if str(type(item))=="<class 'str'>":
                if not checkDuplicate(item,matches[0:count_]):
                    header_list.append(item)
            elif str(type(item))=="<class 'tuple'>":
                header_list.append(item[0]+" "+item[1])
            count_=count_+1
        

        return header_list



    def createWholeText(self,json_path,height,width):
        text_cordinates=[]
        matches=[]

        with open(json_path) as json_file:
            data=json.load(json_file)
            for blocks in data['Blocks']:
                temp_str=''
                #top_key= int(blocks['Geometry']['BoundingBox']['Top']*(height)/10) 
                #Left_key= int(blocks['Geometry']['BoundingBox']['Top']*(height)/10)
                    
                a=blocks["Geometry"]["Polygon"][0]['X']
                b=blocks["Geometry"]["Polygon"][0]['Y']

                c=blocks["Geometry"]["Polygon"][1]['X']
                d=blocks["Geometry"]["Polygon"][1]['Y']
                    
                if (blocks["BlockType"]=='LINE')  :
                    text_cordinates.append((blocks["Text"],a,b,c,d))
                
        count=0  

        for item in text_cordinates:
            sub_list=text_cordinates[count+1:count+20]
            temp_matches=[]
            distance_counter=[]
            count2=0

            for item2 in sub_list:
        
                a=(item[1],item[2])
                b=(item2[1],item2[2])
                dst = sp_distance.euclidean(a, b)
                myradians = math.atan2(b[1]-a[1], b[0]-a[0])
                degrees = math.degrees(myradians)
                degrees=int(degrees)
                #print(degrees,' -- ',item[0],' -- ',item2[0],' -- ',dst)
                if degrees>-4 and degrees<4 and dst<0.39:
                    temp_matches.append( (item[0],item2[0],degrees,dst) )
        
                

            temp_matches=findLowestScore(temp_matches) 

            if len(temp_matches)==0:
                matches.append((item[0]))
            else:
                for i in temp_matches:
                    #print("item",i)
                    matches.append(i)
            count+=1
     
        header_list=[]
        count_=0
        for item in matches:
            #print(str(type))
            if str(type(item))=="<class 'str'>":
                if not checkDuplicate(item,matches[0:count_]):
                    header_list.append(item)
            elif str(type(item))=="<class 'tuple'>":
                header_list.append(item[0]+" "+item[1])
            count_=count_+1
        

        return header_list

    #Create Footer text by Distance 
    def createTextFooter(self,json_path,height,width,top,bottom):
        text_cordinates=[]
        matches=[]

        with open(json_path) as json_file:
            data=json.load(json_file)
            for blocks in data['Blocks']:
                temp_str=''
                #top_key= int(blocks['Geometry']['BoundingBox']['Top']*(height)/10) 
                #Left_key= int(blocks['Geometry']['BoundingBox']['Top']*(height)/10)
                    
                a=blocks["Geometry"]["Polygon"][0]['X']
                b=blocks["Geometry"]["Polygon"][0]['Y']
                c=blocks["Geometry"]["Polygon"][1]['X']
                d=blocks["Geometry"]["Polygon"][1]['Y']
                    
                if (blocks["BlockType"]=='LINE' and blocks['Geometry']['BoundingBox']['Top']>bottom):
                    text_cordinates.append((blocks["Text"],a,b,c,d))
                
        count=0  

        for item in text_cordinates:
            sub_list=text_cordinates[count+1:count+20]
            temp_matches=[]
            distance_counter=[]
            count2=0

            for item2 in sub_list:
        
                a=(item[3],item[4])
                b=(item2[1],item2[2])
                dst = sp_distance.euclidean(a, b)
                myradians = math.atan2(b[1]-a[1], b[0]-a[0])
                degrees = math.degrees(myradians)
                degrees=int(degrees)
                #print("Footer ",degrees,' -- ',item[0],' -- ',item2[0],' -- ',dst)
                if degrees>-4 and degrees<=4 and dst<0.50:
                    temp_matches.append( (item[0],item2[0],degrees,dst) )
        
                

            temp_matches=findLowestScore(temp_matches) 

            if len(temp_matches)==0:
                matches.append((item[0]))
            else:
                for i in temp_matches:
                    #print("item",i)
                    matches.append(i)
            count+=1
     
        footer_list=[]
        count_=0
        for item in matches:
            #print(str(type))
            if str(type(item))=="<class 'str'>":
                if not checkDuplicate(item,matches[0:count_]):
                    footer_list.append(item)
            elif str(type(item))=="<class 'tuple'>":
                footer_list.append(item[0]+" "+item[1])
            count_=count_+1
        

        return footer_list  

    def findLineItemsHeaders(self,df_csv):

        remove_list=[]
        for index, row in df_csv.iterrows():
                match_count=0
                for k,v in row.iteritems():
                        
                        #print(k,v,index)
                        for item in header_items:
                            ratio=fuzz.ratio(str(item).lower().strip(),str(v).lower().strip())
                            #print(ratio,item,' -- ',v)
                            if ratio>75:
                                match_count=match_count+1
                                
                if match_count>=4:
                    remove_list.append(index)
        if len(remove_list)>0:
            df_csv.columns = df_csv.iloc[remove_list[0]]
            df_csv = df_csv[remove_list[0]+1:]
        

        
        return df_csv

    def cleanExtraRows(self,df_csv):

        all_nan=[]
        for index, row in df_csv.iterrows():
            nan_count=0
            allnan=True
            for k,v in row.iteritems():
                if not str(v)=="nan":
                        allnan=False
            if allnan:
                all_nan.append(index)

        for item in all_nan:
             df_csv=df_csv.drop(item,axis=0)
   

 


        remove_list=[]
        for index, row in df_csv.iterrows():
                nan_count=0
                for k,v in row.iteritems():
                        #print(k,v,index)
                        if str(v)=="nan":
                            nan_count+=1
                        if nan_count>3:
                            remove_list.append(index)
        #print(remove_list)
        #df_csv = df_csv[0:] 
        remove_list = list(dict.fromkeys(remove_list))
        for item in remove_list:
             df_csv=df_csv.drop(item,axis=0)
       
        return df_csv


    #get Confidence level 
    def get_confidence_matrix(self,data):
        confidence_dict={}
        
        #with open(json_path) as json_file:
        #    data=json.load(json_file)
        table_blocks = []
        for blocks in data['Blocks']:
            if blocks["BlockType"]=='LINE':
                #print(blocks["Confidence"],blocks["Text"])
                confidence_dict[str(blocks["Text"]).strip().replace(',','') ]=blocks["Confidence"]
                    

        return confidence_dict


        
    def get_dict_key_name(self,d,item):

        for i in d:
            if item.strip().lower() in d[i]:
                return i
        return None


    #DataFrame to o/p compatible Dictionry :
    def df_to_dict(self,df_csv,confidence_dict,config_name):
        total_items=[]
        #print("df to dict")
        #print(df_csv)

        header_list=get_liheader_fields(config_name)
        header_list=[x.lower().strip() for x in header_list]

        header_dict_list=get_header_fields(config_name)
        d={} 
        for i in header_dict_list: 
            #print(i["name"]) 
            temp=i["field_names"]
            temp=[x.lower().strip() for x in temp] 
            d[i["name"]]=temp
            #header_list_tuple.append((i["name"],i["fieldname"])) 
        
        for k,v in df_csv.to_dict().items():
            #print(k,' -- ',v)
            line_items=[]
            for key,value in v.items():
                #print(k,' -- ',value )
                line_items.append({k:value})
            #print("line item conf",line_items)
            total_items.append(line_items)

 
        
        final_dict=[]
        #for item in total_items:
        match_item=None
        #counter=0
        if len(total_items)>0:
            for x in range(0,len(total_items[0])):
                temp_dict=[]
                for y in range(0,len(total_items)):
                    
                    for k1,v1 in total_items[y][x].items():
                        conf=""
                        #print(str(v1).strip() in confidence_dict.keys(),str(v1) )
                        match_item=self.findBestMatch(str(v1).strip(),confidence_dict.keys())
                        if match_item is not None:
                            if len(str(v1).strip())>0:
                                #print(str(v1).strip(),' -- ',match_item)
                                conf=confidence_dict[match_item] 
                            

                        header_mask=None
                        #find nearest header name to this key 
                        #Ratios = process.extract(k1.lower().strip(),header_list)
                        max=0
                        max_el=None
                        for i in header_list:
                            ratio=fuzz.ratio(str(k1).lower().strip(),str(i).lower().strip())
                            if ratio>max and ratio>80:
                                
                                max=ratio
                                max_el=i

                        
                        
                        if max_el is not None: #len(Ratios)>0:
                            #if Ratios[0][1]>=75:
                            header_mask=max_el
                            header_name=self.get_dict_key_name(d,header_mask)
                            if header_name is not None:
                                header_mask=header_name
                            else:
                                header_mask=k1
                        else:
                            header_mask=k1


                        
                        """
                        if str(v1).strip() in confidence_dict.keys():
                            conf=confidence_dict[str(v1).strip() ]
                        else:
                            conf="-"
                        """    

                            
                        temp_dict.append( {str(header_mask):str(v1),"confidence_level":conf}  )
                #temp_dict.append( {'id':counter}  )
                #counter=counter+1
                final_dict.append( {"fields":temp_dict} )
        
        return final_dict


    def findBestMatch(self,str_,match_list):

        max_score=0
        match_item=None

        for item in match_list:
            match=fuzz.partial_ratio(item,str_)
            if match > max_score:
                max_score=match
                match_item=item
        
        return match_item







        #Find Vendor Name by Position 
    def getVendorNameByPosition(self,header_list,company_list):
            
        vendor_name=None
        for item in header_list:
            for company in company_list:
                ratio=fuzz.token_set_ratio(item.lower(),company.lower())
                print(ratio,item.lower(),company.lower())
                if ratio>75:
                    vendor_name=item
                    break
            if not vendor_name==None:
                break
        return vendor_name

    #Detects where Line Items End in Table 
    def LineItemEnding_row(self,invoice_arr,count):
        total_count=[]
        total_row_count=0
        for row in invoice_arr:
            
            for col in row:

                if len(col)<3:
                    continue

                for i1 in tax_items:
                    
                    #ratio=fuzz.partial_ratio(str(col).lower().strip(),i1.lower().strip())
                    ratio=fuzz.ratio(str(col).lower().strip(),i1.lower().strip())
                     
                    if ratio>80 and total_row_count>count:
                        print("tax item: ",i1,"  col:  ",col, " ratio:",ratio)
                        return total_row_count

                for i1 in total_items:
                    #ratio=fuzz.partial_ratio(str(col).lower().strip(),i1)
                    ratio=fuzz.ratio(str(col).lower().strip(),i1.lower().strip())
                   
                    if ratio>80 and total_row_count>count:
                        print("total item: ",i1,"  col:  ",col, " ratio:",ratio)
                        return total_row_count
            total_row_count=total_row_count+1
        return total_row_count


    def findLineItemTable_raw (self,csv_path,config_name):

        #Find Number of Files in the Directory 
        import os
        import pandas as pd
        path, dirs, files = next(os.walk(csv_path))
        file_count = len(files)
        print("file _count",file_count)
        print("csv_file",csv_path)
        invoice_table=""
        df_ = pd.DataFrame()
        if file_count==1:
            df=pd.read_csv(csv_path+'/'+files[0])
            
            break_outer=False
            for i in range(len(df)) :
                for item in df.iloc[i]:

                    for i1 in tax_items:
                        ratio=fuzz.partial_ratio(str(item).lower().strip(),i1.lower().strip())
                        if ratio>90:
                            break_outer=True

                    for i1 in total_items:
                        ratio=fuzz.partial_ratio(str(item).lower().strip(),i1.lower().strip())
                        if ratio>90:
                            break_outer=True   
                    

                if break_outer==True:
                    break
                df_=df_.append(df.iloc[i])
            
            df_columns=df_.columns.tolist()
            db_headers=get_header_fields(config_name) 
            columns_new=getLineItemHeaderName(db_headers,df_columns)
            print("columns diff")
            #print("old columns ",df_columns)
            print("new columns",df_ )
            df_.columns=columns_new

            return df_
        return None

           
            
        
        



    def findLineItemTable (self,csv_path):

        #Find Number of Files in the Directory 
        import os
        import pandas as pd
        path, dirs, files = next(os.walk(csv_path))
        file_count = len(files)
        print("file _count",file_count)
        print("csv_file",csv_path)
        invoice_table=""
        if file_count>1:
            header_found=False
            for f in files:

                df_csv=pd.read_csv(csv_path+'/'+f, delimiter = ',',header=None)

                for index, row in df_csv.iterrows():
                    match_count=0
                    for k,v in row.iteritems():
                            
                            if len(v)>40:
                                continue
                            #print("header ",k,v,index)
                            for item in header_items:
                                ratio=fuzz.partial_ratio(str(item).lower().strip(),str(v).lower().strip())
                                #print(ratio,item,' -- ',v)
                                if ratio>75:
                                    match_count=match_count+1
                    print("---------------------")  
                    print("match_count",match_count)                     
                    if match_count>=3:
                        header_found=True
                        invoice_table=f
                        break
        else:
            invoice_table=files[0]
        
        return invoice_table
    

    # Matches a list to a string and returns 
    # partial ratio amonst the string 
    def match_item_list(self,match_list,item):
        
        matches=[]
        for i in match_list:
            if len(i.strip())>0:
                ratio=fuzz.ratio(str(item).lower().strip(),str(i).lower().strip())
                #print(ratio,i,' -- ',item)
                matches.append((i,ratio))
            
        print("before sorting ",matches)
        matches.sort(key=lambda x:x[1],reverse=True)
        print("after sorting ",matches)
        return matches[0]
        


   
    def findLineItemTable_V2 (self,csv_path,config_name):


        #Find Number of Files in the Directory 
        
        path, dirs, files = next(os.walk(csv_path))
        file_count = len(files)
        print("file _count",file_count)
        print("csv_file",csv_path)

        invoice_table=""
        scores_dict={}

        #Fetch the HeaderList
        db_headers=get_header_fields(config_name) 


        for f in files:
            scores=[]
            df_csv=pd.read_csv(csv_path+'/'+f, delimiter = ',',header=None)

            for index, row in df_csv.iterrows():
                match_count=0
                
                #Matching for Header Items 
                for k,v in row.iteritems():
                    if len(str(v))>40:
                        continue
                    
                    #for item in header_items:
                    for l_item in db_headers:
                        field_names=l_item["field_names"] 
                        ratio=self.match_item_list(field_names,v)
                        ratio=ratio[1]
                        #ratio=fuzz.partial_ratio(str(item).lower().strip(),str(v).lower().strip())
                        #ratio2=distance.get_jaro_distance(str(item).lower().strip(),str(v).lower().strip(), winkler=True, scaling=0.1)
                        #print(ratio,l_item,' -- ',v)
                        if ratio>80:
                            match_count=match_count+1
                            #print(ratio,l_item,' -- ',v)
                
                scores.append(match_count)

            scores_dict[f]=max(scores)

        return_file=None
        highiest=0
        print("File checking scores dict ",scores_dict)
        for keys in scores_dict:
            if highiest<scores_dict[keys]:
                highiest=scores_dict[keys]
                return_file=keys

        return return_file
    
    #Finds Line Item Using Table Cordinates
    # Performs Type Checking of All Line Items 
    def findLineItems_V3(self,csv_path,json_path,config_name):


        invoice_arr = list(csv.reader(open(csv_path)))
        count=0
        header_match_counts={}
        #Fetch the HeaderList
        db_headers=get_header_fields(config_name) 

        for row in invoice_arr:
            match_count=0
            
            for col in row:
                
                
                       
                #for item in header_items:
                for l_item in db_headers:
                    field_names=l_item["field_names"] 
                    ratio=self.match_item_list(field_names,col)
                    #print(col,l_item,' : ',ratio)
                    ratio=ratio[1]
                    #ratio=fuzz.partial_ratio(str(item).lower().strip(),str(col).lower().strip())
                    print(" >>>>>> ",field_names,' : ',col,' : ',ratio)
                    #ratio2=distance.get_jaro_distance(str(item).lower().strip(),str(v).lower().strip(), winkler=True, scaling=0.1)
                    if ratio>80:
                        match_count=match_count+1
                        #print(ratio,item,' -- ',v,ratio2)
             
            print("Match Count",match_count)
            print("------------------------------------------------------------")

            db_logger.info("Headers Match Count "+str(match_count),{"user":"" ,"entity":""} )
            db_logger.info("Headers Match Threshold "+str(3),{"user":"" ,"entity":""})

            if match_count>=2:
                #print("found Line Item Header")
                #print(invoice_arr[count], "Header Number " ,count )
                #break
                header_match_counts[count]=match_count

            count=count+1 
        print("header match counts ",header_match_counts.items())

        if len(header_match_counts)==0:
            return pd.DataFrame(),0,0 
        maximum = max(header_match_counts.items(), key=lambda k: k[1])
        print("header maximum ",maximum)
        count=maximum[0]
        
        #Find Lien Items Till Subtotal or total :

        #Find the Line Item Ending Part
       
        print("*******************invoice arr ******************************")
        print(invoice_arr)
        print("*******************invoice arr *******************************")
        
        line_item_end_count= self.LineItemEnding_row(invoice_arr,count) 
        
        print("Line Item End Count ",line_item_end_count,"count :",count)

        #invoice_arr[total_row_count-1]    
        #invoice_arr=invoice_arr.remove( invoice_arr[total_row_count-1] )

        

       

        invoice_arr_li=None
        invoice_arr_whole=invoice_arr

        if line_item_end_count is not None:
            #invoice_arr=invoice_arr[count:line_item_end_count+1]
            invoice_arr=invoice_arr[count+1:line_item_end_count]

        
         #removing empty rows from data 
        a=[]
        for i in invoice_arr:
            if len([x for x in i if x])>0:
                a.append(i)
        
        invoice_arr=a

        print("invoice_arr ")
        print(invoice_arr)
        
        print("Initial Line Item *************** ")
        print(invoice_arr)
        print("Initial Line Item *************** ")

        print("line item count ",count ,len(invoice_arr))
        try:
            header=invoice_arr_whole[count]
        except:
            return pd.DataFrame(),0,0
        

        print("headers")
        print(header)

        header_checks={}
        c=0
        for i in header:
            for j in header_items_type :

                ratio=fuzz.partial_ratio(str(i).lower().strip(),str(j).lower().strip())
                #print(ratio,i,j,header_items_type[j])
                if(ratio>80):
                    print(ratio,i,j,header_items_type[j])
                    header_checks[c]=[i,header_items_type[j]]
            
            
               
                
                        
            c=c+1
        
        
    
        print("******* header checks **************")
        print(header_checks)
        print("******* header checks **************")

       
        """
        if c==count:
                c=c+1
                continue
            else:
        """

        #Performing Type Checking 
        c=0
        f_line_items=[]
         

        ####################### START IF SKIP TYPE CHEKINNG -- UNCOMMENT THIS BLOCK ################# 
        
        for row in invoice_arr:
            f_line_items.append(c)
            c=c+1
        
        ####################### END IF SKIP TYPE CHEKINNG -- UNCOMMENT THIS BLOCK #################


         
        ####################### START IF SKIP TYPE CHEKINNG #################            
            
        """ 
        for row in invoice_arr:

            print("row checking" , row)
        
            
            cc=0
            score_check=[]
            for col in row:
                if cc in header_checks:
                    col_type=header_checks[cc][1]
                    print("Matching| type",col_type," col:",col)
                    t=col_type[0]
                    p=intPercent(col)

                    if t=="num":
                        if p>=40:
                            score_check.append(1)
                    elif t=="string":
                        if p<40:
                            score_check.append(1)
                    elif t=="":
                        if len(col)>5:
                            score_check.append(1)
                    
                         
                            
                                
                            
                        
                
                        
                cc=cc+1
            print("length of scorecheck ",len(score_check))
            if(len(score_check)>=2):
                f_line_items.append(c)
                    
            c=c+1
            """
        ####################### END IF SKIP TYPE CHECKING #################    
        print("******************LINE ITEMS ***************************")
        search_line=[]
        lineitems=[]
        db_logger.info("Line Items Detected :",{"user":"" ,"entity":""} )
        for item in f_line_items:
            print(invoice_arr[item])
            db_logger.info( "item : "+ str(invoice_arr[item]).replace('%','') ,{"user":"" ,"entity":""} )
            lineitems.append( invoice_arr[item] )
            search_line= invoice_arr[item]

        print("******************LINE ITEMS ***************************")

        #convert invoice into matrix , along with cordinates 
        invoice_arr_dict,all_arr=createInvocieArray(json_path)
        
        #search_line=f_line_items[len(f_line_items)-1]
        print("search line ",search_line)
        bottom=0
        top=0
        for item in all_arr:
            res=intersect(search_line,item)
            if len(res)>2:
                print("bottom",res,getcordinates(item,invoice_arr_dict))
                bottom= getcordinates(item,invoice_arr_dict)
        print("count ",count)
        header_row=invoice_arr_whole[count]
        for item in all_arr:
            res=intersect(header_row,item)
            if len(res)>2:
                print("top",res,getcordinates(item,invoice_arr_dict))
                top= getcordinates(item,invoice_arr_dict)
        
        print("top",top)
        print("bottom",bottom)
        
        #replace headers with header names :
        header_row_new=getLineItemHeaderName(db_headers ,header_row)
        df = pd.DataFrame(lineitems, columns = header_row_new ) 

        print("dataframe v3",df)
     





        return df,top,bottom
        
            






    #######################Finding Line Items by patterns ######################################
    #Finds Line Item Without Table Coridnates 
    #Hunts for tables, and Decides Line Items 
    def findLineItems_bypatterns (self,json_path):


        text_cordinates=[] 
        with open(json_path) as json_file:
            data=json.load(json_file)
            for blocks in data['Blocks']:

                temp_str=''
                a=blocks["Geometry"]["Polygon"][0]['X']
                b=blocks["Geometry"]["Polygon"][0]['Y']
               
                c=blocks["Geometry"]["Polygon"][1]['X']
                d=blocks["Geometry"]["Polygon"][1]['Y']

                if (blocks["BlockType"]=='LINE' )  :
                    text_cordinates.append((blocks["Text"],round(b*100), ((a+c)/2)*100,a*100, (c-a)*100,b ))
         
         #find all horizontal lines :
        line_dict={}
        for item in text_cordinates:
            line_no=item[1]
            line_no_a=line_no-1
            if (line_no in line_dict)  :
                temp=line_dict[line_no]
                temp.append(item)
                line_dict[line_no]=temp
            else:
                temp=[]
                temp.append(item)
                line_dict[line_no]=temp

        invoice_arr=[]
        invoice_arr_dict={}
        rows=0
        for item in line_dict.keys():
            cols=0
            temp=[]
            for i in line_dict[item]:
                temp.append(i[0])
                cols=cols+1
            rows=rows+1
            invoice_arr.append(temp)
            invoice_arr_dict[line_dict[item][0][5]]=temp
         
        #print("invoice_arr") 
        #print(invoice_arr) 
        #Identifying the Header Items 
        
        
        count=0
        header_match_counts={}
        for row in invoice_arr:
            match_count=0
            
            for col in row:
                
                
                for item in header_items:
                    ratio=fuzz.partial_ratio(str(item).lower().strip(),str(col).lower().strip())
                    #print("ratio >>>>",ratio,item,col)
                    #ratio2=distance.get_jaro_distance(str(item).lower().strip(),str(v).lower().strip(), winkler=True, scaling=0.1)
                    if ratio>75:
                        match_count=match_count+1
                        #print(ratio,item,' -- ',v,ratio2)
            #print(header_items)
            #print("match count",match_count)
            if match_count>=2:
                #print("found Line Item Header")
                #print(invoice_arr[count],count )
                header_match_counts[count]=match_count 
                
            count=count+1


            #Finding the row of total or subtotal :
        

        #finding max value in dictionry 
        if len(header_match_counts)==0:
            return None,0,0
            
        maximum = max(header_match_counts.items(), key=lambda k: k[1])
        print("maximum ",maximum)
        count=maximum[0]

        print("selected invoice header is : ",invoice_arr[count], " count: " ,count)


        # New Logic For Line Items Start  ############
        
        #get datatyes of headers 
        #print(invoice_arr[count])
        datatypes={}
        c=0
        for i in invoice_arr[count]:
            for j in header_items_type.keys():
                
                r=fuzz.partial_ratio(i.lower(),j.lower())
                if r > 80:
                    datatypes[c]=j
            c=c+1
        
        h_count=len(invoice_arr[count])
        line_items=[]
        header_row= invoice_arr[count]
        for row in invoice_arr[count+1:len(invoice_arr)-1]:
            #print(len(row),range(h_count-1,h_count+1))
            if (len(row) in range(h_count-1,h_count+1)):
                #match=checkTypes(row)
                if len(row)<h_count:
                    row.extend(['']*(h_count-len(row)))
                elif h_count > len(row):
                    header_row.extend(['']*(len(row)-h_count) )

                line_items.append(row)
        

        # New Logic For Line Items End ###############
        """
        total_count=[]
        total_row_count=0
        for row in invoice_arr:
            for col in row:
                if(len(col)>=len("total")):
                    ratio=fuzz.partial_ratio(str(col).lower().strip(),"total")
                    
                if ratio>90 and total_row_count>count:
                    total_count.append( (row,total_row_count,ratio) )
                    break
            total_row_count=total_row_count+1

        #Finalizing the Line Items into an array  :
        print("total count rows")
        print(total_count) 
        no_cols=len(invoice_arr[count])
        #print("no of columns ",no_cols)
        line_items_s1=[]
        c=0

        for row in invoice_arr:
            #and  len(row) in range(no_cols-3,no_cols+2)
            if c>=count and c<total_count[0][1] :
                line_items_s1.append(row)
            c=c+1
        """    
        print(" ***********************LINE ITEMS********************* ")
        print(line_items)
        print(" ***********************LINE ITEMS********************* ")
        
        if len(line_items)==0:
            return None,0,0
        top=0
        bottom=0
        last_line_item=line_items[len(line_items)-1]

        print("last line item ",last_line_item)
        print("header line item",invoice_arr[count])
        for item in invoice_arr_dict:
            if invoice_arr_dict[item]==last_line_item:
                bottom=item
                #print("bottom",invoice_arr_dict[item],item)
            if invoice_arr_dict[item]==invoice_arr[count]:
                top=item
                #print("top ",invoice_arr_dict[item],item)

        print("top",top)
        print("bottom",bottom)

        #validating Line Items With DataTypes
        #Identifying the Header Data Type Items 
        
        #print(line_items)
        print("______________________")
        print(invoice_arr[count])
         


        df = pd.DataFrame(line_items, columns = header_row) 
        print("dataframe ",df)
            
        print("*******************FINAL LINE ITEMS *************************")
    



        return df,top,bottom




    #exclude line item as per configuration

    def lineitemexclusions(self,config_name,df):
        exclusion_list=getlineitemexclusions(config_name) 
        exclusion_list=[x for x in exclusion_list if x]
         
        exclusion_rows=[] 
        for ix,c in df.iterrows():
            row_itemlist=list(c.to_dict().values() )




            for i in exclusion_list:

                for j in row_itemlist: 
                    
                    #reomve percentage from text
                    try:
                        j=re.sub(r'(100|(\d{1,2}(\.\d+)*))%', '', j)
                        #Remove paranthesis from text
                        j=re.sub(r'\([^)]*\)', '', j)


                        ratio=fuzz.ratio(i.lower().strip(), j.lower().strip() )
                        if ratio>75:
                            exclusion_rows.append(ix)
            
                    except:
                        continue
        df=df.drop(df.index[exclusion_rows])

        return df


           
            
            


   

                    



                      

                   


            
            
    


        




                    
            



        
        
        
                    













        
            




