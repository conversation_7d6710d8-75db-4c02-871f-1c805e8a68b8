from Invoice.models import *

#fetch the extraction list from configuration name 

#fetching only the address entities 
def get_Address_fields(config_name):


    #Fetching the extract group id 
    res=Configuration.objects.filter(name=config_name)
    extract_field_id=None
    extract_kvalue=[]
    if res.exists():
        config=res.first()
        extract_field_id=config.extract_field
    else:
        #fetch default configuration global_config
        res=Configuration.objects.filter(name='global_config')
        config=res.first()
        extract_field_id=config.extract_field

    address_entity=[]

    if extract_field_id is not None:
        res_a=AddressEntity.objects.filter(extractfield=extract_field_id)

        for extract_field in res_a:
                #extract_field=field_mapping_q.first()
                extract_f_dict={}
                extract_f_dict["field"]=extract_field.extractfield
                extract_f_dict["fieldtype"]=extract_field.fieldtype
                extract_f_dict["str"]=extract_field.longstring
                extract_f_dict["entity"]=extract_field.entity
                address_entity.append(extract_f_dict)
                
    return address_entity



#fetching  general entities 
def get_General_entity(config_name):


    #Fetching the extract group id 
    res=Configuration.objects.filter(name=config_name)
    extract_field_id=None
    extract_kvalue=[]
    if res.exists():
        config=res.first()
        extract_field_id=config.extract_field
    else:
        #fetch default configuration global_config
        res=Configuration.objects.filter(name='global_config')
        config=res.first()
        extract_field_id=config.extract_field

    general_entity=[]

    if extract_field_id is not None:
        res_a=GeneralEntity.objects.filter(extractfield=extract_field_id)

        for extract_field in res_a:
                #extract_field=field_mapping_q.first()
                extract_f_dict={}
                extract_f_dict["field"]=extract_field.name
                extract_f_dict["field"]=extract_field.extractfield
                extract_f_dict["fieldtype"]=extract_field.fieldtype
                extract_f_dict["str"]=extract_field.fullstring
                extract_f_dict["entity"]=extract_field.entity
                general_entity.append(extract_f_dict)
                
    return general_entity

def getquerybyvendorname(config_name,vendorname):

    #Fetching the extract group id 
    res=Configuration.objects.filter(name=config_name)
    extract_field_id=None
    extract_kvalue=[]
    if res.exists():
        config=res.first()
        extract_field_id=config.extract_field
    else:
        #fetch default configuration global_config
        res=Configuration.objects.filter(name='global_config')
        config=res.first()
        extract_field_id=config.extract_field
    l=[]
    if extract_field_id is not None:
        query_res=Query.objects.filter(extractfield=extract_field_id,vendorfieldnames=vendorname) 
        if query_res.exists():
            
            for item in query_res:
                q={}
                q["fieldname"]=item.fieldname.strip()
                q["query"]=item.query.strip()
                l.append(q)

    return l

def getquery(config_name):

    #Fetching the extract group id 
    res=Configuration.objects.filter(name=config_name)
    extract_field_id=None
    extract_kvalue=[]
    if res.exists():
        config=res.first()
        extract_field_id=config.extract_field
    else:
        #fetch default configuration global_config
        res=Configuration.objects.filter(name='global_config')
        config=res.first()
        extract_field_id=config.extract_field
    l=[]
    if extract_field_id is not None:
        query_res=Query.objects.filter(extractfield=extract_field_id) 
        if query_res.exists():
            
            for item in query_res:
                q={}
                q["fieldname"]=item.fieldname.strip()
                q["query"]=item.query.strip()
                q["json"]=item.vendorfieldnames
                l.append(q)

    return l


def get_companynames(config_name):
    res=Configuration.objects.filter(name=config_name)
    companynames=[]
    if res.exists():
        config=res.first()
        companynames=config.company_names
    
    if len(companynames)>0:
        return companynames.strip().split(',')
    
def checkconfignameexists(config_name):
    res=Configuration.objects.filter(name=config_name.strip())
    if res.exists():
        return True
    else:        
        return False

def get_extraction_list(config_name,algo_type,masking_info=False,hascondition_=False):
    
    # res=Configuration.objects.filter(name=config_name)
    # extract_field_id=None
    # extract_kvalue=[]
    # if res.exists():
    #     config=res.first()
    #     extract_field_id=config.extract_field
    # else:
    #     #fetch default configuration global_config
    #     res=Configuration.objects.filter(name='global_config')
    #     config=res.first()
    #     extract_field_id=config.extract_field

    # print("extract field id ",extract_field_id) 
    # if extract_field_id is not None:
    #     mapping_id_q=ExtractFieldMapping.objects.filter(field=extract_field_id,active=True) 
    #     kvalue_id=[]

    #     for r in mapping_id_q:
    #         kvalue_id.append(r.value.id)

    #     print(kvalue_id ,"algo type ",algo_type," hascondition_ ",hascondition_)

    #     if algo_type is not None:
    #         if masking_info==True:
    #             field_mapping_q=  kvalue.objects.filter(id__in=kvalue_id,f_algo_type=algo_type,masking=True,hascondition=hascondition_ )
    #         else:
    #             field_mapping_q=  kvalue.objects.filter(id__in=kvalue_id,f_algo_type=algo_type,hascondition=hascondition_ )

    #     else:
    #         if masking_info==True:
    #             field_mapping_q=  kvalue.objects.filter(id__in=kvalue_id,masking=True,hascondition=hascondition_ )
    #         else:
    #             field_mapping_q=  kvalue.objects.filter(id__in=kvalue_id,hascondition=hascondition_ )

    extract_kvalue=[]

    config = Configuration.objects.filter(name=config_name).first()
    if not config:
        config = Configuration.objects.filter(name='global_config').first()
    extract_field_id = getattr(config, 'extract_field', None)

    if not extract_field_id:
        return []

    mapping_ids = ExtractFieldMapping.objects.filter(field=extract_field_id, active=True).values_list('value_id', flat=True)

    filters = {
        'id__in': mapping_ids,
        'hascondition': hascondition_,
    }
    if algo_type:
        filters['f_algo_type'] = algo_type
    if masking_info:
        filters['masking'] = True

    field_mapping_q = kvalue.objects.filter(**filters)        
        
    if field_mapping_q.exists():

        for extract_field in field_mapping_q:
            #extract_field=field_mapping_q.first()
            extract_f_dict={}
            extract_f_dict["name"]=extract_field.f_name
            extract_f_dict["type"]=extract_field.f_type
            extract_f_dict["head"]=extract_field.f_head
            extract_f_dict["tail"]=extract_field.f_tail
            extract_f_dict["strategy"]=extract_field.f_strategy
            extract_f_dict["rule"]=extract_field.f_rule
            extract_f_dict["conditionfield"]=extract_field.conditionfieldnames
            extract_f_dict["hascondition"]=extract_field.hascondition
            
            if extract_field.f_regex_field is not None:
                RegexGroupValues= RegexGroupValue.objects.filter(field=extract_field.f_regex_field.id )
                regex_id=[]

                for r in RegexGroupValues:
                    regex_id.append(r.value.id)
                regex=  Regex.objects.filter(id__in=regex_id )
                extract_f_dict["regex"]=list(regex.values())
            else:
                extract_f_dict["regex"]=None
            extract_kvalue.append(extract_f_dict)          
            
    return extract_kvalue

def get_extraction_list_from_db(config_name):
    
    extract_kvalue=[]

    config = Configuration.objects.filter(name=config_name).first()
    if not config:
        config = Configuration.objects.filter(name='global_config').first()
    extract_field_id = getattr(config, 'extract_field', None)

    if not extract_field_id:
        return []

    mapping_ids = ExtractFieldMapping.objects.filter(field=extract_field_id, active=True).values_list('value_id', flat=True)

    filters = {
        'id__in': mapping_ids
    }

    field_mapping_q = kvalue.objects.filter(**filters)        
        
    if field_mapping_q.exists():

        for extract_field in field_mapping_q:
            #extract_field=field_mapping_q.first()
            extract_f_dict={}
            extract_f_dict["name"]=extract_field.f_name
            extract_f_dict["type"]=extract_field.f_type
            extract_f_dict["head"]=extract_field.f_head
            extract_f_dict["tail"]=extract_field.f_tail
            extract_f_dict["strategy"]=extract_field.f_strategy
            extract_f_dict["rule"]=extract_field.f_rule
            extract_f_dict["conditionfield"]=extract_field.conditionfieldnames
            extract_f_dict["hascondition"]=extract_field.hascondition
            extract_f_dict["algo_type"] = extract_field.f_algo_type
            extract_f_dict["masking"] = extract_field.masking
            if extract_field.f_regex_field is not None:
                RegexGroupValues= RegexGroupValue.objects.filter(field=extract_field.f_regex_field.id )
                regex_id=[]

                for r in RegexGroupValues:
                    regex_id.append(r.value.id)
                regex=  Regex.objects.filter(id__in=regex_id )
                extract_f_dict["regex"]=list(regex.values())
            else:
                extract_f_dict["regex"]=None
            extract_kvalue.append(extract_f_dict)          
    return config, extract_kvalue

def get_VendorFields(config_name):



    #Fetching the extract group id 
    res=Configuration.objects.filter(name=config_name)
    extract_field_id=None
    extract_kvalue=[]
    if res.exists():
        config=res.first()
        extract_field_id=config.extract_field
    else:
        #fetch default configuration global_config
        res=Configuration.objects.filter(name='global_config')
        config=res.first()
        extract_field_id=config.extract_field

    vendorfields=[]

    if extract_field_id is not None:
        res_a=VendorFields.objects.filter(extractfield=extract_field_id)

        for extract_field in res_a:
                #extract_field=field_mapping_q.first()
               
                fieldnames=extract_field.fieldnames
                fieldarray=fieldnames.split(",")
                vendorfields.extend(fieldarray)
              
                
    return vendorfields


#fetches Line Item Headers 
def get_header_fields(config_name):

    res=Configuration.objects.filter(name=config_name)
    extract_field_id=None
    if res.exists():
        config=res.first()
        extract_field_id=config.extract_field

    if extract_field_id is not None:
        header_res=LineItemHeaders.objects.filter(field=extract_field_id,active=True) 
        
        header_list=[]
        if header_res.exists():
            
            for header in header_res:
                temp_dict={}
                temp_dict["name"]=header.name
                l=header.field_names.split(",")
                l=[item.strip() for item in l]  
                temp_dict["field_names"]=l
                header_list.append(temp_dict)

    return header_list


def getlineitemexclusions(config_name):


    #fetching extract field id 
    res=Configuration.objects.filter(name=config_name)
    extract_field_id=None
    exclusion_list=[]
    if res.exists():
        config=res.first()
        extract_field_id=config.extract_field

        if extract_field_id is not None:
            lineitemsex_res=LineItemExclusion.objects.filter(field=extract_field_id,active=True) 
        
        if lineitemsex_res.exists():
            
            for item in lineitemsex_res:
                items_split=item.field_list.split(",")
                exclusion_list.extend(items_split)
    
    return exclusion_list
    

def get_ai_fieldmatch_threshold(config_name):

    res=Configuration.objects.filter(name=config_name)
    
    if res.exists():
        config=res.first()
        
    else:
        #fetch default configuration global_config
        res=Configuration.objects.filter(name='global_config')
        config=res.first()
    
    threshold=config.ai_fieldmatch_threshold
    return threshold

def get_max_pagecount(config_name):

    res=Configuration.objects.filter(name=config_name)
    
    if res.exists():
        config=res.first()
        
    else:
        #fetch default configuration global_config
        res=Configuration.objects.filter(name='global_config')
        config=res.first()
    
    max_pagescan=config.max_pagescan
    return max_pagescan