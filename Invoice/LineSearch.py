from pyjarowinkler import distance as d
from rapidfuzz import fuzz
import re
from AMS.extract_settings import *
from spacy.matcher import Matcher
from Invoice.ExtractUtils import *
import math 
import datefinder


alpha_neumeric_regex = "([$a-zA-Z0-9/-]+[0-9]+)|([0-9+]+[$0-9a-zA-Z_/-]+)$"
date_name_parser = "\w+\s+(date)"
float_regex = "\d+\.\d+"
num_string = "[0-9][0-9.]*[0-9]"
string="[a-zA-Z0-9].*"



def getSearchableRow(dataframe, searchlist):

    return_list = []
    for index, row in dataframe.iterrows():

        for k, v in row.iteritems():

            for item in searchlist:

                jratio = d.get_jaro_distance(str(v).lower().replace(
                    '.', ''), str(item).lower().replace('.', ''))

                lratio = fuzz.partial_ratio(str(v).lower().replace(
                    '.', ''), str(item).lower().replace('.', ''))

                if (jratio*100) > 85 or lratio > 85:
                    return_list.append(index)

    return return_list

def searchRegex(regex,text):

    res=re.search(regex,text,re.IGNORECASE)
    result=[]
    if res:
        print("regex match ", res.group())
        result.append(res.group())
        return result
   

def matchRule(rule_dict,text):

    matcher = Matcher(nlp.vocab, validate=True)

    for key in rule_dict:
        matcher.add(key, None, rule_dict[key] )
    
    doc = nlp( text )
    matches = matcher(doc)
    res=[]
    for match_id, start, end in matches:
        span = doc[start:end]
        match_res=span.text
        res.append(match_res)
    return res

def match_date(text):
    matches = datefinder.find_dates(text)
    res=[]
    for match in matches:
        res.append(match)
    
    return res


def searchRegex(regex,text):
    res=re.search(regex,text,re.IGNORECASE)
    result=[]
    if res:
        print("regex match ", res.group())
        result.append(res.group())
        return res.group()
    else:
        return None

def matchAdvancedRule(rule_dict,text,return_label):
    group_details = rule_dict['group_details']
    group_details_df = pd.DataFrame(group_details)
    group_details_df = group_details_df.sort_values(['rule_group_id'], ascending=[True])
    adv_match_result = []
    for _,group in group_details_df.groupby("rule_group_id"):
        group = group.sort_values(['priority_index'], ascending=[True])
        group_res = []

        for row in group.iterrows():
            rd = {}
            terminate_if_match = row[1]['terminate_if_match']
            for rule_name in row[1]['rules']:
                rd[rule_name] = rule_dict[rule_name]

            
            ####
            matcher = Matcher(nlp.vocab, validate=True)
            for key in row[1]['rules']:
                matcher.add(key, None, rule_dict[key] )

            # Extracting entity type in rules
            entity_type=None
            r=rd
            key=list(r.keys() )[0]
            for i in r[key]:
                k=list(i.keys())[0]
            if k=="ENT_TYPE":
                entity_type=i[k]
            # Extracting Entity Type in Rules 
            
            
            doc = nlp( text )
            matches = matcher(doc)
            res=[]
            for match_id, start, end in matches:
                string_id = nlp.vocab.strings[match_id]
                span = doc[start:end]
                match_res=span.text
                if(return_label):
                    res.append(row[1]['rule_name'])
                else:
                    res.append(match_res)

            res = list(set(res))
            if len(res)>0:
                result = max(res, key = len)
                res2=None
                doc1=nlp(result)

                for t in doc1.ents:
                    if t.label_==entity_type:
                        res2=t.text
                # print("result found rule match ",res2)
                element_found = False
                if res2 is not None:
                    group_res.append(res2) 
                    element_found = True
                else :
                    # print("result none , result found ",result)
                    # print(" res ***** ",res)
                    group_res.append(result)
                    element_found = True
                adv_match_result = adv_match_result + group_res
                if(element_found and terminate_if_match):
                    break
    # print("advance match result", adv_match_result )        
    return adv_match_result
    

def matchRule(rule_dict,text):
    print("in matchrule ")
    # print("text is ")
    # print(text)
    extraction_rule=None
    if "extraction_rule" in rule_dict:
        extraction_rule=rule_dict["extraction_rule"]
        #del rule_dict["extraction_rule"]
    
    # print("extraction_rule ",extraction_rule)
    
    if(extraction_rule != None):    
        final_result=None
        matcher = Matcher(nlp.vocab, validate=True)

        for key in rule_dict:
            if key !="extraction_rule":
                # print("adding matcher",rule_dict[key])
                matcher.add(key, None, rule_dict[key] )

        # Extracting entity type in rules
        entity_type=None
        r=rule_dict
        key=list(r.keys() )[0]
        for i in r[key]:
            k=list(i.keys())[0]
        if k=="ENT_TYPE":
            entity_type=i[k]
        # Extracting Entity Type in Rules
        
        doc = nlp( text )
        matches = matcher(doc)
        res=[]
        for match_id, start, end in matches:
            span = doc[start:end]
            match_res=span.text
            res.append(match_res)
        
        print("Matching Performed..")
        # print("results after matcher ",res)
        if len(res)>0:
            result = max(res, key = len)
            res2=None
            doc1=nlp(result)

            for t in doc1.ents:
                #print(t.label_)
                if t.label_==entity_type:
                    res2=t.text
            # print("result found rule match ",res2)
            if res2 is not None:
                #return res2
                final_result=res2
            else :
                #return result
                final_result=result

            ext_matcher = Matcher(nlp.vocab, validate=True)

            
            ext_matcher.add("result_extract", None, extraction_rule  )
            doc3 = nlp( text )
            ext_match = ext_matcher(doc3)
            res_final=None
            for match_id, start, end in ext_match:
                span = doc[start:end]
                match_res=span.text
                res_final=match_res
                #res.append(match_res)
                
            # print("final extraction value ",res_final)
            return res_final

        else:
            return None
    else:
        return None

def matchRuleForValue(rule_dict,text):

    matcher = Matcher(nlp.vocab, validate=True)

    for key in rule_dict:
        matcher.add(key, None, rule_dict[key] )

    # Extracting entity type in rules
    entity_type=None
    r=rule_dict
    key=list(r.keys() )[0]
    for i in r[key]:
        k=list(i.keys())[0]
    if k=="ENT_TYPE":
        entity_type=i[k]
    # Extracting Entity Type in Rules
       
    
    doc = nlp( text )
    matches = matcher(doc)
    res=[]
    for match_id, start, end in matches:
        #span = doc[start:end]
        #match_res=span.text
        string_id = nlp.vocab.strings[match_id]
        res.append(string_id)
        #res.append(match_res)
    res = list(set(res))
    if len(res)>0:
        result = max(res, key = len)
        res2=None
        doc1=nlp(result)

        for t in doc1.ents:
            #print(t.label_)
            if t.label_==entity_type:
                res2=t.text
        print("result found rule match ",res2)
        if res2 is not None:
            return res2
        else :
            return result
    else:
        return None

def searchRegexArr2(regexarr,text):
        result=None
        for regex in regexarr:
            
            #p=regex.compile(regex)
            res=re.search(regexarr[0][0],text,re.IGNORECASE)
            if res:
                if regexarr[0][1] is None:
                    print("regex match ", res.group())
                    #result.append(res.group())
                    return res.group()
                else:
                    print("regex match ", res.group(regexarr[0][1]))
                    return res.group(regexarr[0][1]) 
        return result
def custom_round(value):
    fractional_part, whole_part = math.modf(value)
    if fractional_part >= 0.6:
        return math.ceil(value)
    else:
        return math.floor(value)

class DummyLogger:
    def print(self,*args):
        print(" ".join(str(item) for item in args))
        
class LineSearch:
    def __init__(self):
        ...


    def createLines(self,data):

        text_cordinates=[] 
        #with open(json_path) as json_file:
        #    data=json.load(json_file)
        for blocks in data['Blocks']:
            temp_str=''
            a=blocks["Geometry"]["Polygon"][0]['X']
            b=blocks["Geometry"]["Polygon"][0]['Y']
                
            c=blocks["Geometry"]["Polygon"][3]['X']
            d=blocks["Geometry"]["Polygon"][3]['Y']
            block_top=blocks['Geometry']['BoundingBox']['Top']
            if (blocks["BlockType"]=='LINE'    )  :
                # text_cordinates.append((blocks["Text"],math.floor(((b+d)/2)*100), ((a+c)/2)*100,a*100, (c-a)*100,b ))
                text_cordinates.append((blocks["Text"],custom_round(((b+d)/2)*100), ((a+c)/2)*100,a*100, (c-a)*100,b ))
        line_dict={}

        for item in text_cordinates:
            
            line_no=item[1]
            line_no_a=line_no-1
            #line_no_b=line_no+1
            #print(line_dict.keys())
            if (line_no in line_dict)  :
                #print(line_no)
                temp=line_dict[line_no]
                temp.append(item)
                line_dict[line_no]=temp
            else:
                temp=[]
                temp.append(item)
                line_dict[line_no]=temp
        
        keyset=list(line_dict.keys())

        mean_distance=0
        c=0
        for i in keyset:
            if c==0:
                c=c+1
                continue
            
            #print(keyset[c] , keyset[c-1])
            mean_distance+= (keyset[c]-keyset[c-1])
            c=c+1
        
        include_range=1

        
        if c>0: 
            if mean_distance/c>2:
                include_range=math.floor((mean_distance/c)/2 )

        
        lines=[]
        new_dict={}
        check_lines=[]
        for key in line_dict.keys():
            temp=[]
            if key in check_lines:
                #print(key ,"found in check_lines")
                continue
            r1=range(key,key+include_range)
            l_keys=set(line_dict.keys())
            intersection=l_keys.intersection(r1)
            #print(key,'intersection',list(intersection) )
            check_lines.extend(list(intersection))
            temp=" "
            for item in list(intersection):
                l=line_dict[item]
                for i in l:
                    temp=temp+i[0]+" "
            
            lines.append(temp)

        return lines
    
    def append_results(self,res, results):
        if isinstance(res, list):
            if len(res) > 0:
                results.extend(res)  # Use extend for lists
        elif res is not None:
            results.append(res)
        return results

    def search_field(self,lines,f_details):
        # print(f_details)
        # name=f_details["name"]
        # rules=f_details["rule"]
        #type=f_details["rule"]
        # match_result=[]
        if f_details["name"] == "currency":
            print("currency")
        if(f_details["type"]=="Rule" or f_details["type"]=="Rule for Value" ):
            rule=f_details["rule"]       
            if("group_details" in rule and ("*" in [item.strip() for item in f_details["head"]] or "*" in [item.strip() for item in f_details["tail"]])):
                for line in lines:
                    result=matchAdvancedRule(rule,line,f_details["type"]=="Rule for Value") 
                    if(isinstance(result,list) and len(result) > 0 ):
                        return result[0]
                return None
        #if f_details["head"] and  f_details["tail"]:
        #    for h in f_details["head"]:
        #        for t in f_details["tail"]:
        #            word_list.append(h.strip()+" "+t.strip())
        word_list = {
                        f"{h.strip()} {t.strip()}".strip()
                        for h in f_details["head"]
                        for t in f_details["tail"]
                        if f"{h.strip()} {t.strip()}".strip()
                    }
        word_list = list(word_list)

        ###################
        ##########Finding the list of matching keywords
        match_ratio_word=[]
        highiest=0
        for word in word_list:

            for item in lines:
                match_ratio = fuzz.partial_ratio(item.lower().strip(), word.lower().strip())
                #jaro_distance= distance.get_jaro_distance(item.lower().strip(), word.lower().strip())
                #self.cl.print("-->"+str(item.lower().strip())+str(word.lower().strip()) +str(match_ratio))
                if match_ratio>85 and match_ratio>highiest:
                    #highiest=match_ratio
                    match_ratio_word.append(item)
        match_ratio_word = list(set(match_ratio_word))
        # print("Best Match Found ",match_ratio_word)

        #longest_match=match_ratio_word

        #print("longest_match found",longest_match)
        
        if len(match_ratio_word)==0:
            return None

        #extract value
        results=[]
        rule=f_details["rule"]  
        is_grouped = "group_details" in rule  # Check for "group_details" only once
        match_string = " ".join([item for item in match_ratio_word])

        if f_details["type"] in ["Rule", "Rule for Value"]:
            res = None
            if f_details["type"] == "Rule":
                if is_grouped:
                    res = matchAdvancedRule(rule, match_string, False)
                else:
                    res = matchRule(rule, match_string)
            elif f_details["type"] == "Rule for Value":
                if is_grouped:
                    res = matchAdvancedRule(rule, match_string, True)
                else:
                    res = matchRuleForValue(rule, match_string)
            
            # Append results based on type of `res`
            result = self.append_results(res, results)
        else:

            for longest_match in match_ratio_word :
                res=None
                longest_match=str(longest_match).replace(",",'')
        
                if f_details["type"]=="Numstring":
                    res=searchRegex(num_string,longest_match)

                
                elif f_details["type"]=="Alpha":
                    res=searchRegex(alpha_neumeric_regex,longest_match)

                elif f_details["type"]=="Regex":
                    regarr=[]
                    for i in f_details["regex"]:
                        regarr.append((i['regex'],i['regex_index']))
        
                    res = searchRegexArr2(regarr, longest_match)
                    #res=searchRegex(f_details["regex"],longest_match)

                if(isinstance(res, list)):
                    if(len(res) > 0):
                        results = results + res
                else:
                    if res is not None:
                        results.append(res)

        if len(results)>0:
            return max(results, key=len)  
        
        return None

    
    

            
        
    


        

        
        







            
    
       


     
            
        
        
        

        





  


