from Invoice.FieldSearch import FieldSearch
from Invoice.LineSearch import *
from Invoice.DbConfig import *
from Invoice.QueryConditions import *
import time

def getFormFieldsbyDistance(config_name,form_fields,data,db_extrtaction_list,table_dimensions,hascondition_=False):

    fieldsearch=FieldSearch()
    #Fetch fields to be extracted from database 
    #db_extrtaction_list=get_extraction_list(config_name,'NearestDistance',hascondition_=hascondition_)
    print("Nearest Distance condition ",db_extrtaction_list)

    FieldSearch.config_name=config_name
    #json_file = open(json_path)
    #data=json.load(json_file)
    for extract_field in db_extrtaction_list:

        f_name = None
        f_type = None
        f_head = None
        f_tail = None
        f_regex = None
        f_entityname = None
        f_rule = None

        if "name" in extract_field.keys():
            f_name = extract_field["name"]
        
        if "type" in extract_field.keys():
            f_type = extract_field["type"]
        
        if "head" in extract_field.keys():
            f_head = extract_field["head"]
            if len(f_head.strip())>0:
                f_head=f_head.split(",")
        
        if "tail" in extract_field.keys():
            f_tail = extract_field["tail"]
            if len(f_tail.strip())>0:
                f_tail=f_tail.split(",")
        
        if "regex" in extract_field.keys():
            f_regex = extract_field["regex"]

        if "entity_name" in extract_field.keys():
            f_entityname = extract_field["regex"]

        if "rule" in extract_field.keys():
            f_rule = extract_field["rule"]
        has_cond = extract_field.get("hascondition", False)
        
        # fuzz_result = None
        # regex_result = None
        # date_result = None
        # match_result = None

        field_details={}
        field_details["name"]=f_name
        field_details["type"]=f_type
        field_details["head"]=f_head
        field_details["tail"]=f_tail
        field_details["regex"]=f_regex
        field_details["entity_name"]=f_entityname
        field_details["rule"]=f_rule

        print("extract field has condition",extract_field["hascondition"])
        
        # if extract_field["hascondition"]==True and hascondition_==True:
        #     #extract the condition
        #     #compare the condiion with json 
        #     #if match occurs only then field search should be executed
        #     condition_res=matchjson_and_condition(form_fields,extract_field["conditionfield"])
        #     print("condition_res ",condition_res)
        #     if condition_res:
        #         field_value,field_conf= fieldsearch.search_field(json_path,field_details,table_dimensions[0],table_dimensions[1],"LINE" )
        #         print("extracted value ",field_value,field_conf)
        #         if not field_value==None:
        #             data={f_name.strip():str(field_value).strip(),"confidence_level":field_conf }
        #             form_fields.append(data) 
        # elif hascondition_==False:
         

        #     field_value,field_conf= fieldsearch.search_field(json_path,field_details,table_dimensions[0],table_dimensions[1],"LINE" )
           
        #     if not field_value==None:
        #         data={f_name.strip():str(field_value).strip(),"confidence_level":field_conf }
        #         form_fields.append(data)

        if extract_field["hascondition"]==True and hascondition_==True:
            condition_res=matchjson_and_condition(form_fields,extract_field["conditionfield"])
            # print("condition_res ",condition_res)
            proceed = condition_res
        else:
            proceed = not has_cond

        if proceed:
            field_value,field_conf= fieldsearch.search_field(data,field_details,table_dimensions[0],table_dimensions[1],"LINE" )
            print("extracted value ",field_value,field_conf)
            if not field_value==None:
                form_fields.append({f_name.strip():str(field_value).strip(),"confidence_level":field_conf }) 

    return form_fields


def getFormFieldsbyLinear(config_name, form_fields, json_path, flat_fields, hascondition_=False):
    print("In Linear Form Fields")

    # Overall function timing
    function_start_time = time.time()

    # Time tracking for LineSearch constructor
    start_time = time.time()
    linesearch=LineSearch()
    end_time = time.time()
    print(f"Time taken for LineSearch constructor: {end_time - start_time:.4f} seconds")

    # Time tracking for createLines method
    start_time = time.time()
    lines=linesearch.createLines(json_path)
    end_time = time.time()
    print(f"Time taken for createLines method: {end_time - start_time:.4f} seconds")

    # Time tracking for get_extraction_list function
    start_time = time.time()
    #flat_fields=get_extraction_list(config_name,'Horizontal',hascondition_=hascondition_)
    end_time = time.time()
    print(f"Time taken for get_extraction_list function: {end_time - start_time:.4f} seconds")
    res=None
    for extract_field in flat_fields:
        f_name=None
        f_type=None
        f_regex=None
        f_rule=None

        if "name" in extract_field.keys():
            f_name = extract_field["name"]

        if "type" in extract_field.keys():
            f_type = extract_field["type"]
         
        if "regex" in extract_field.keys():
            f_regex = extract_field["regex"]                

        if "rule" in extract_field.keys():
            f_rule = extract_field["rule"]

        if "head" in extract_field.keys():
            f_head = extract_field["head"]
            if len(f_head.strip())>0:
                f_head=f_head.split(",")
        
        if "tail" in extract_field.keys():
            f_tail = extract_field["tail"]
            if len(f_tail.strip())>0:
                f_tail=f_tail.split(",")
                    
        # Searching by Fuzzy Search
        field_details={}
        field_details["name"]=f_name
        field_details["type"]=f_type          
        field_details["regex"]=f_regex
        field_details["rule"]=f_rule
        field_details["head"]=f_head
        field_details["tail"]=f_tail

        if extract_field["hascondition"]==True and hascondition_==True:
            #extract the condition
            #compare the condiion with json
            #if match occurs only then field search should be executed

            # Time tracking for matchjson_and_condition function
            start_time = time.time()
            condition_res=matchjson_and_condition(form_fields,extract_field["conditionfield"])
            end_time = time.time()
            print(f"Time taken for matchjson_and_condition (field: {f_name}): {end_time - start_time:.4f} seconds")

            if condition_res:
                # Time tracking for search_field method (with condition)
                start_time = time.time()
                res=linesearch.search_field(lines,field_details)
                end_time = time.time()
                print(f"Time taken for search_field with condition (field: {f_name}): {end_time - start_time:.4f} seconds")

                if not res==None:
                    data={f_name.strip():res.strip(),"confidence_level":95 }
                    form_fields.append(data)

        elif hascondition_==False:
            # Time tracking for search_field method (without condition)
            start_time = time.time()
            res=linesearch.search_field(lines,field_details)
            end_time = time.time()
            print(f"Time taken for search_field without condition (field: {f_name}): {end_time - start_time:.4f} seconds")

            if not res==None:
                data={f_name.strip():res.strip(),"confidence_level":95 }
                form_fields.append(data)

    filtered_form_fields = []
    [filtered_form_fields.append(x) for x in form_fields if x not in filtered_form_fields]

    # Overall function timing
    function_end_time = time.time()
    print(f"Total time taken for getFormFieldsbyLinear function: {function_end_time - function_start_time:.4f} seconds")

    # print("Linear form fields ",res)
    return filtered_form_fields,lines,flat_fields
            
          
