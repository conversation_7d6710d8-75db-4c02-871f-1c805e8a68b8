import pandas as pd
import boto3
import json
import time
from concurrent.futures import ThreadPoolExecutor


class Responselayer:
    executed = False
    response = None
    fields = {}
    lineitems = None
    vendor_name = None
    structured_lineitems = None

    def _process_image(self, textract, image_path):
        with open(image_path, "rb") as document:
            res = textract.analyze_expense(Document={'Bytes': document.read()})

        fields_local = {}
        structured_lineitems = []

        if len(res["ExpenseDocuments"]) > 0:
            for i in res["ExpenseDocuments"][0]['SummaryFields']:
                if "LabelDetection" in i:
                    self.fields[i["LabelDetection"]["Text"]] = i["ValueDetection"]["Text"]
                    fields_local[i["LabelDetection"]["Text"]] = i["ValueDetection"]["Text"]
                else:
                    self.fields[i["Type"]["Text"]] = i["ValueDetection"]["Text"]
                    fields_local[i["Type"]["Text"]] = i["ValueDetection"]["Text"]

        ven_name = fields_local.get("VENDOR_NAME", None)

        lineitems_data = []
        if len(res["ExpenseDocuments"]) > 0 and len(res["ExpenseDocuments"][0]["LineItemGroups"]) > 0:
            for item in res["ExpenseDocuments"][0]["LineItemGroups"][0]['LineItems']:
                fields = item['LineItemExpenseFields']
                d = {}
                temp_dict = []

                for ii in fields:
                    key = ii["LabelDetection"]["Text"] if "LabelDetection" in ii else ii["Type"]["Text"]
                    val = ii["ValueDetection"]["Text"]
                    conf = ii["ValueDetection"]["Confidence"]
                    d[key] = val
                    temp_dict.append({key: val, "confidence_level": conf})

                lineitems_data.append(d)
                structured_lineitems.append({"fields": temp_dict})

        df = pd.DataFrame(lineitems_data) if lineitems_data else pd.DataFrame()
        if not df.empty and 'EXPENSE_ROW' in df:
            df = df.drop('EXPENSE_ROW', axis=1)

        return {
            "response": res,
            "structured_lineitems": structured_lineitems,
            "lineitems_df": df,
            "vendor_name": ven_name
        }

    def execute(self, images):
        print("images response layer", images)
        #START TIME TO CALCULATE SECONDS TAKEN BY THE FUNCTION


        start = time.time()
        # Create Textract client once
        textract = boto3.client(
            'textract'
        )

        dfs = []
        structured_lineitems_total = []
        ven_name = None

        with ThreadPoolExecutor(max_workers=4) as executor:
            futures = {
                executor.submit(self._process_image, textract, path): key
                for key, path in images.items()
            }

            for future in futures:
                result = future.result()

                if not self.executed:
                    self.executed = True
                self.response = result["response"]

                if result["vendor_name"]:
                    self.vendor_name = result["vendor_name"]
                    ven_name = result["vendor_name"]
                else:
                    print("NO API VENDOR NAME FOUND")

                structured_lineitems_total.extend(result["structured_lineitems"])
                if not result["lineitems_df"].empty:
                    dfs.append(result["lineitems_df"])

        self.structured_lineitems = structured_lineitems_total
        self.lineitems = pd.concat(dfs, sort=False) if dfs else None
        end = time.time()
        print("Time taken by response layer", end - start)
        return ven_name