from pyjarowinkler import distance as d

from itertools import groupby

from fuzzywuzzy import fuzz
import re
from AMS.extract_settings import *

from spacy.matcher import Matcher
from Invoice.ExtractUtils import *
import math 
from scipy.spatial import distance as sp_distance
from fuzzywuzzy import fuzz 
from fuzzywuzzy import process 
import datefinder
from spacy.matcher import <PERSON>rase<PERSON><PERSON><PERSON>,Matcher
from Invoice.DbConfig import *
from spacy.tokens import Span
matcher = PhraseMatcher(nlp2.vocab)
alpha_neumeric_regex = "([$a-zA-Z0-9/-]+[0-9]+)|([0-9+]+[$0-9a-zA-Z_/-]+)$"
date_name_parser = "\w+\s+(date)"
float_regex = "\d+\.\d+"
num_string = "[0-9][0-9.]*[0-9]"
string="[a-zA-Z0-9].*"



def getSearchableRow(dataframe, searchlist):

    return_list = []
    for index, row in dataframe.iterrows():

        for k, v in row.iteritems():

            for item in searchlist:

                jratio = d.get_jaro_distance(str(v).lower().replace(
                    '.', ''), str(item).lower().replace('.', ''))

                lratio = fuzz.partial_ratio(str(v).lower().replace(
                    '.', ''), str(item).lower().replace('.', ''))

                if (jratio*100) > 85 or lratio > 85:
                    return_list.append(index)

                    #
                    # print(row)

    return return_list

def searchRegex(regex,text):
    res=re.search(regex,text,re.IGNORECASE)
    result=[]
    if res:
        print("regex match ", res.group())
        result.append(res.group())
        return result

def searchRegexArr(regexarr,text):
    result=[]
    for regex in regexarr:
        p=regex.compile(regex)
        res=p.search(regex,text)
        if res:
            print("regex match ", res.group())
            result.append(res.group())
            return result
    return result

def searchRegexArr2(regexarr,text):
    result=[]
    compiled_regexes = [re.compile(r, re.IGNORECASE) for r in regexarr]

    for regex in compiled_regexes:
        res = regex.search(text)
        if res:
            print("regex match ", res.group())
            result.append(res.group())
            return result
    return result
        
def parseaddress(dist_list,f_details,text_dict,dist_list_keys):
    loc={}

    print("config name ",FieldSearch().config_name)
    
    add_fields=get_Address_fields(FieldSearch().config_name)
    general_entity_fields=get_General_entity(FieldSearch().config_name)

    places=[]
    places_entity=[]
    city_countries=[]
    city_countries_entity=[]
    ventity=[]

    for item in add_fields:
        if item["fieldtype"]=="PLACE":
            places.append( (item['str'],item['entity'] ))
            places_entity.append(item['entity'])
        if item["fieldtype"]=="CITY-COUNTRY":
            city_countries.append( (item['str'],item['entity']) )
            city_countries_entity.append( item['entity'] )
    
    for item in general_entity_fields:
        #print("item ",item)
        if item['fieldtype']=="ORG":
            #entity_str.append( (item['str'],item['entity'] ) )
            ventity.append(item['entity'])
    # print("places ",places)
    
    city_countries_entity.extend(places_entity)
    city_countries_entity.extend(ventity)
    entity_club=city_countries_entity
    # print("entity club",entity_club)

    if len(entity_club)>0:
        patterns = [nlp2(entity) for entity in entity_club]
        matcher.add("LOC_PATTERN", patterns)
    #else:
    #    matcher.add("LOC_PATTERN", [])
    
    last_line=None
    for index,i in enumerate(dist_list_keys[0:10]):
        
        
        
        
        key=dist_list[i][0]
        degree=dist_list[i][1]
        
        text=text_dict[key][0]
        x= text_dict[key][1]
        y=text_dict[key][2]
        i_confidence=text_dict[key][6]
        if text!="":
            doc=nlp2(text)
            doc_ent=list(doc.ents)
            matches = matcher(doc)
        
        #print("last line ",last_line)
        #check if last line and current line fall in same paragraph 
        if last_line is not None:
            x_last = text_dict[last_line][1]
            y_last = text_dict[last_line][2]

            print("last line diff ", (y-y_last)*100,text )
            if ((y-y_last)*100)>2:
                break

        last_line=key
        match_span=[]

        for match_id, start, end in matches:
            match_span.append( (start,end) ) #doc[start:end]
        
        LOC=doc.vocab.strings[u'GPE']

        if match_span is not None:
            for i in match_span:
                t=Span(doc,i[0],i[1],label="GPE")
                doc_ent=doc_ent+[t]
        
        for t in doc_ent:
            label=t.label_
            if label=="LOC" or label=="GPE" or label=="FAC" or label=="ORG":

                #Excluding text 

                for item in add_fields:
                    if item["fieldtype"]=="EXCLUDE" and fuzz.partial_ratio(item['str'],text)>80:
                        continue


                loc[index]=(text,i,x,y)

    #removing pattern from matcher
    matcher.remove("LOC_PATTERN")
    group=[]
    b=[]
    print("location extracted ....",loc)
    l=list(loc.keys())
    str=""
    for k,v in loc.items() :
        str=str+v[0]+" , "
    
    return str,70

       
def matchAdvancedRuleOLD(rule_dict,text,return_label):
    group_details = rule_dict['group_details']
    group_details_df = pd.DataFrame(group_details)
    group_details_df = group_details_df.sort_values(['rule_group_id'], ascending=[True])
    adv_match_result = []
    for _,group in group_details_df.groupby("rule_group_id"):
        group = group.sort_values(['priority_index'], ascending=[True])
        #group_res = []

        for row in group.iterrows():
            rd = {}
            terminate_if_match = row[1]['terminate_if_match']
            for rule_name in row[1]['rules']:
                rd[rule_name] = rule_dict[rule_name]
            
            matcher = Matcher(nlp.vocab, validate=True)
            
            for key in row[1]['rules']:
                matcher.add(key, None, rd[key] )

            doc = nlp( text )
            matches = matcher(doc)
            #matches2 = pharase_matcher(doc)


            res=[]

            for match_id, start, end in matches:
                
                span = doc[start:end]
                match_res=span.text
                if(return_label):	
                    res.append(row[1]['rule_name'])	
                else:
                    res.append(match_res)

            res = list(set(res))
            if len(res)>0:
                result = max(res, key = len)
                adv_match_result.append(result)
                if(terminate_if_match):
                    break
    return adv_match_result

def matchAdvancedRule(rule_dict, text, return_label):
    group_details = rule_dict['group_details']

    # Sort the group_details first by group_id then by priority_index
    sorted_details = sorted(group_details, key=lambda x: (x['rule_group_id'], x['priority_index']))

    adv_match_result = []

    # Group by rule_group_id
    for rule_group_id, group_items in groupby(sorted_details, key=lambda x: x['rule_group_id']):
        group_items = list(group_items)  # materialize group

        for rule_def in group_items:
            terminate_if_match = rule_def.get('terminate_if_match', False)
            rule_name = rule_def.get('rule_name', '')
            rule_keys = rule_def.get('rules', [])

            # Build matcher
            matcher = Matcher(nlp.vocab, validate=True)
            for rule_key in rule_keys:
                patterns = rule_dict.get(rule_key)
                if patterns:
                    matcher.add(rule_key, [patterns])  # spaCy expects list of patterns

            doc = nlp(text)
            matches = matcher(doc)

            # Extract matched strings
            res = []
            for match_id, start, end in matches:
                span = doc[start:end]
                if return_label:
                    res.append(rule_name)
                else:
                    res.append(span.text)

            # Deduplicate
            res = list(set(res))

            if res:
                result = max(res, key=len)
                adv_match_result.append(result)
                if terminate_if_match:
                    break

    return adv_match_result


def matchRule(rule_dict,text):
    ################# Add patterns for entity in config  
    gen_fields=get_General_entity(FieldSearch().config_name)
    entity = [item['entity'] for item in gen_fields]
    # Setup PhraseMatcher with entity patterns
    phrase_matcher = PhraseMatcher(nlp.vocab)

    if len(entity)>0:
        patterns = [nlp(e) for e in entity]
        phrase_matcher.add("ENTITY_PATTERN", patterns)
    
    matcher = Matcher(nlp.vocab, validate=True)
    
    for key in rule_dict:
        matcher.add(key, None, rule_dict[key] )

    doc = nlp( text )
    matches = matcher(doc)
    matches2 = phrase_matcher(doc)


    res=[]

    for match_id, start, end in matches:
        span = doc[start:end]
        match_res=span.text
        res.append(match_res)

    for match_id, start, end in matches2:
        span = doc[start:end]
        match_res=span.text
        res.append(match_res)
    
    #checking phrase matcher 
    if len(res)>0:
        result = max(res, key = len)
        return [result]
    else:
        return None


def matchRuleForValue(rule_dict,text):
    
    matcher = Matcher(nlp.vocab, validate=True)
    
    for key in rule_dict:
        matcher.add(key, None, rule_dict[key] )
        
    doc = nlp( text )
    matches = matcher(doc)

    res=[]

    for match_id, start, end in matches:
        string_id = nlp.vocab.strings[match_id]
        res.append(string_id) 

    res = list(set(res))
    
    #checking phrase matcher 
    if len(res)>0:
        result = max(res, key = len)
        return [result]
    else:
        return None

def match_date(text):
    res=[]
    try:
        matches = datefinder.find_dates(text)
        match_list=list(matches)
        for match in match_list:
            res.append(match)
    except:
        print("datefinder error occured while parsing ")
    
    return res

def calculate_confidence(confidence,degree,distance):
    degree__=degree
    deg_conf=0
    #Angle of key value pairs 
    if degree in range(-4,4):
        if degree<0:
            degree_=degree*-1
        deg_conf=degree_*4
    elif degree in range(99,70):
        degree__=90-degree
        if degree<0:
            degree_=degree*-1
        deg_conf=degree_*4

    #Distance of Key Value pairs 
    distance= (int)(distance*3)

    confidence=(confidence-deg_conf)-distance
    return confidence

class DummyLogger:
    def print(self,*args):
        print(" ".join(str(item) for item in args))

class FieldSearch:
    config_name="global_config"
    
    def __init__(self,cl=None):
        if(cl is None):
            cl = DummyLogger()
        self.cl = cl

    def search_fieldOLD(self,json_path,f_details,top,bottom,linetype):
        
        # self.cl.print("top .... ",top)
        # self.cl.print("bottom...... ",bottom)

        if top==None or bottom==None:
            top=0
            bottom=0
        # self.cl.print("********************",f_details["name"],"******************************")

        text_dict={}
        text_cordinates=[]
        #create text list for finding the word list
        with open(json_path) as json_file:
            data=json.load(json_file)
        
            for blocks in data['Blocks']:        
                a=blocks["Geometry"]["Polygon"][0]['X']
                b=blocks["Geometry"]["Polygon"][0]['Y']
                c=blocks["Geometry"]["Polygon"][1]['X']
                d=blocks["Geometry"]["Polygon"][1]['Y']
                median=( (a+c)/2,(b+d)/2)
                    #print(blocks["Geometry"]["Polygon"])
                    # blocks['Geometry']['BoundingBox']['Top'] 
                    # (int)(b*100),(int)(((a+c)/2)*100)
                block_top=blocks['Geometry']['BoundingBox']['Top']
                

                if ( blocks["BlockType"]==linetype   and (block_top<top or block_top>bottom)   )  :
                    confidence=blocks["Confidence"]
                    text_cordinates.append((blocks["Text"],a,b,c,d,median,confidence ))
                    text_dict[blocks["Id"]]= (blocks["Text"],a,b,c,d,median,confidence)
        

        #Form all the probable Keyoword combinations for search :
        if(f_details["type"]=="Rule" or f_details["type"]=="Rule for Value" ):
            rule=f_details["rule"]       
            if("group_details" in rule and ("*" in [item.strip() for item in f_details["head"]] or "*" in [item.strip() for item in f_details["tail"]])):
                text = " ".join([text_dict[item][0] for item in text_dict])
                result=matchAdvancedRule(rule,text,f_details["type"]=="Rule for Value") 
                if(isinstance(result,list) and len(result) > 0 ):
                    return result[0],100
                else:
                    return None,0
               
        word_list = []   
        for h in f_details["head"]:
            for t in f_details["tail"]:
                word_list.append(h.strip()+" "+t.strip())
         
        #Finding the list of matching keywords
        match_ratio_word=None
        highiest=0
        for word in word_list:

            for item in text_cordinates:
                match_ratio=0
                try:
                    match_ratio = fuzz.ratio(item[0].lower().strip(), word.lower().strip())
                    # self.cl.print("-->"+item[0].lower().strip()+ word.lower().strip()+str(match_ratio))
                    if match_ratio>85 and match_ratio>highiest:
                        # self.cl.print("-->"+item[0].lower().strip()+ word.lower().strip()+str(match_ratio))
                        highiest=match_ratio
                        match_ratio_word=item
                    #jaro_distance= distance.get_jaro_distance(item[0].lower().strip(), word.lower().strip())
                except:
                    pass
                
        #No Best Matching Words Found
        if match_ratio_word==None:
            return None,0
        
        #to over load paramters for search overload these values 
        if f_details["type"]=="Address":
            r1, r2, r3, r4, dst_range = 0, 80, 79, 120, 0.30
        else:
            r1, r2, r3, r4, dst_range = -4, 80, 79, 130, 0.30


        #Searching for the Keyword in the list 
        dist_list={}
       
        for item in text_dict.keys():
            dst_range=None
            median=text_dict[item][5]
            #b=(match_ratio_word[1], match_ratio_word[2])
            #dst = sp_distance.euclidean(median[0] , median[1] )
                    
            a=(text_dict[item][1], text_dict[item][2])
            b=(match_ratio_word[3], match_ratio_word[4])
            b_=(match_ratio_word[1], match_ratio_word[2])
            
            ax= (text_dict[item][1]+text_dict[item][3])/2
            ay= (text_dict[item][2]+text_dict[item][4])/2

            bx=( match_ratio_word[1] + match_ratio_word[3]) /2
            by=( match_ratio_word[2] + match_ratio_word[4]) /2


            dst = sp_distance.euclidean(b, a)
            #myradians = math.atan2( a[1]-b_[1],a[0]-b_[0] )
            myradians = math.atan2( ay-by,ax-bx)
            degrees = math.degrees(myradians)
            degrees=int(degrees)
            # self.cl.print("### "+text_dict[item][0]+" degrees :"+str(degrees)+" match word :"+str(match_ratio_word[0])+" | distance :"+str(dst))
            if text_dict[item][0]==match_ratio_word[0]:
                degrees=0
                dst=0
            
    
            if (degrees in range(r1,r2) or degrees in range(r3,r4) )  :
                if not dst==0:
                    if dst_range is not None:
                        if dst<0.25:
                            dist_list[dst]=(item,degrees)
                    #else:
                    #    dist_list[dst]=(item,degrees)

        # self.cl.print("*************dist list ****************")    
        # self.cl.print(str(dist_list)) 
        
        dist_list_keys=sorted(dist_list.keys())
        # dist_list_keys.sort(reverse=False)
        
        # for i in dist_list_keys:
        #     key=dist_list[i][0]
        #     degree=dist_list[i][1]
        #     self.cl.print("Nearest matches "+str(i)+ ' '+str(degree)+ ' '+str(match_ratio_word[0])+' '+str(text_dict[key][0]))
        # #print("list of elements ",dist_list)
         
        #print("length of dst list ",dist_list )
        match_list={}
        
        if f_details["type"]=="Address":
            self.cl.print("in address type field ")
            #call the parse address method 
            address,ad_conf=parseaddress(dist_list,f_details,text_dict,dist_list_keys)
            return address,ad_conf

        else:
            #checking fields on single line 
            for index,i in enumerate(dist_list_keys[0:10]):
                key=dist_list[i][0]
                degree=dist_list[i][1]
                text=text_dict[key][0].replace(",","")
                i_confidence=text_dict[key][6]
                result=[]
                # self.cl.print( "dst list "+str(i*100)+str(degree) +str(match_ratio_word[0])+str(text_dict[key][0])+"conf "+str(i_confidence))
                # self.cl.print("*************************")

                #Search by Field Type :
                if f_details["type"]=="Date":
                    result=match_date(text)
                
                elif f_details["type"]=="Numstring":
                    result=searchRegex(num_string,text)
                    #res = re.search(num_string, text)

                elif f_details["type"]=="Rule":
                    rule=f_details["rule"]
                    
                    if("group_details" in rule):
                        result=matchAdvancedRule(rule,text,False) 
                    else:
                        result=matchRule(rule,text)

                elif f_details["type"]=="Rule for Value":
                    rule=f_details["rule"]
                    if("group_details" in rule):
                        result=matchAdvancedRule(rule,text,True)
                    else:
                        result=matchRuleForValue(rule,text)

                elif f_details["type"]=="Alpha":
                    result=searchRegex(alpha_neumeric_regex,text)
                    #res = re.search(alpha_neumeric_regex, text)
                
                elif f_details["type"]=="Float":
                    result=searchRegex(float_regex,text)
                
                elif f_details["type"]=="Regex":
                    regarr=[]
                    for i in f_details["regex"]:
                        regarr.append(i['regex'])
                    result = searchRegexArr2(regarr, text)

                if not result==None and  len(result)>0  :
                    # self.cl.print("indices "+str(i)+str(result)+str(i_confidence))
                    match_list[index]=(result,i_confidence - int(index*10) )
                    #match_list[i]=(result,i_confidence)
                    
                
        
        # self.cl.print("*************"+str(f_details["name"])+"********************")
        # self.cl.print("match list "+str(match_list))
        match_list_keys=sorted(match_list.keys())
        
        # self.cl.print("match list keys "+str(match_list_keys))
        

        if len(match_list_keys)>0:
            r_key=match_list_keys[0]
            self.cl.print(f_details["name"]+' : '+ str(match_list[r_key][0]) +"confidence :"+str(match_list[r_key][1] ))
            return match_list[r_key][0][0],match_list[r_key][1]
        else:
            return None,0
        
    def search_field(self,data,f_details,top,bottom,linetype):
        
        # self.cl.print("top .... ",top)
        # self.cl.print("bottom...... ",bottom)
        if(f_details["type"]=="Rule" or f_details["type"]=="Rule for Value" ):
            if top==None or bottom==None:
                top=0
                bottom=0
            # self.cl.print("********************",f_details["name"],"******************************")

            text_dict={}
            text_cordinates=[]
            #create text list for finding the word list
            
            for blocks in data['Blocks']:        
                a=blocks["Geometry"]["Polygon"][0]['X']
                b=blocks["Geometry"]["Polygon"][0]['Y']
                c=blocks["Geometry"]["Polygon"][1]['X']
                d=blocks["Geometry"]["Polygon"][1]['Y']
                median=( (a+c)/2,(b+d)/2)
                    #print(blocks["Geometry"]["Polygon"])
                    # blocks['Geometry']['BoundingBox']['Top'] 
                    # (int)(b*100),(int)(((a+c)/2)*100)
                block_top=blocks['Geometry']['BoundingBox']['Top']
                

                if ( blocks["BlockType"]==linetype   and (block_top<top or block_top>bottom)   )  :
                    confidence=blocks["Confidence"]
                    text_cordinates.append((blocks["Text"],a,b,c,d,median,confidence ))
                    text_dict[blocks["Id"]]= (blocks["Text"],a,b,c,d,median,confidence)
            

            #Form all the probable Keyoword combinations for search :
        
            rule=f_details["rule"]       
            if("group_details" in rule and ("*" in [item.strip() for item in f_details["head"]] or "*" in [item.strip() for item in f_details["tail"]])):
                text = " ".join([text_dict[item][0] for item in text_dict])
                result=matchAdvancedRule(rule,text,f_details["type"]=="Rule for Value") 
                if(isinstance(result,list) and len(result) > 0 ):
                    return result[0],100
                else:
                    return None,0
        return None,0