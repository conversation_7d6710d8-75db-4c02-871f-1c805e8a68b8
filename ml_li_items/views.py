from AMS.settings import BASE_DIR
import shutil

from PIL import Image
from os import path
import base64
import traceback
            
import os
import json
import boto3
import io
from io import BytesIO
import sys
import os
import io
import magic
import PyPDF2
from AMS.extract_settings import *
from Invoice.ExtractUtils import *
from Invoice.Extract import *
from Invoice.LineSearch import *

from Invoice.ImageManipulation import *
from Invoice.FormFieldExtractionWrapper import *

from Invoice.Forms_Extraction import *
from Invoice.Vendor_Extraction import *
from Invoice.DbConfig import *
from Invoice.FieldMatch import * 

#import spacy
import uuid
import json
from django.http import HttpResponse
from pdf2image import convert_from_path

# Create your views here.

from rest_framework.views import APIView
from Invoice.ML.TFPredictions import *

#New Line items 

from ml_li_items.mlextractLI.markheaders import *
from ml_li_items.mlextractLI.pytorchmodelload import * 
from Invoice.ResponseLayer import *
from custom_logger.custom_logger import CustomLogger
from Invoice.QueryConditions import *
from invoice_tracker.DbInvoiceTracker import *
from InvoiceSplit.utils import *

# Instance of boto3 client 
client = boto3.client(  service_name='textract',region_name='us-east-1',
                        endpoint_url='https://textract.us-east-1.amazonaws.com',
                        aws_access_key_id="********************",
                        aws_secret_access_key="6629LxPd+So34S4EJPxsxs0KOvCwt/AwrjZkAHwN")

class Version(APIView):
    def get(self, request):
        cl = CustomLogger("-")

        version = "0.1"
        try:
            file = open("../version.txt","r")
            version = file.readlines()[0]
            version = str(version)
        except:
            print("No file of version setting static version: 0.1")
        res = {"name":"InvoiceflowAI","response": "ok","version":version}
        cl.print("version is :"+str(version),request.user)

        return HttpResponse(json.dumps(res,indent=4, sort_keys=True, default=str))

# def getPageSize(invoice_path, amplify):
#     try:
#         page_sizes = []
#         with open(invoice_path, 'rb') as file:
#             pdf_reader = PyPDF2.PdfReader(file)

#             # Iterate through each page of the PDF
#             for page_num in range(len(pdf_reader.pages)):
#                 page = pdf_reader.pages[page_num]
#                 page_size = page.cropbox 
#                 page_sizes.append((page_size.upper_right[0]*amplify, page_size.upper_right[1]*amplify))

#     except:
#         page_sizes = []
#     return page_sizes

def getPageSize(invoice_path, amplify):
    try:
        page_sizes = []
        with open(invoice_path, 'rb') as file:
            pdf_reader = PyPDF2.PdfReader(file)
            for page_num in range(len(pdf_reader.pages)):
                page = pdf_reader.pages[page_num]
                box = page.cropbox  # or page.mediabox if you prefer full content
                # Calculate width and height from box coordinates
                width = (float(box.upper_right[0]) - float(box.lower_left[0])) * amplify
                height = (float(box.upper_right[1]) - float(box.lower_left[1])) * amplify
                print(f"Page {page_num+1}: width={width}, height={height}")
                page_sizes.append((width, height))
    except Exception as e:
        print("Error:", e)
        page_sizes = []
    return page_sizes
        

class NewInvoiceAPI(APIView):
    
    def reader(self, file):
        with open(file, "rb") as image_file:
            img_test = image_file.read()
            bytes_test = bytearray(img_test)
        return bytes_test

    def get(self, request):
        res = {"response": "ok","version":"0.1"}
        return HttpResponse(res)
    
    
    def post(self, request):
        request_start = time.time()
        config_name=None
        # Construction of JSON Structures
        unique_filename=None
        invoice_type=None
        config_name=None 

        #response layer object 
        response_layer=Responselayer()
        response_layer.vendor_name=None
        try:
            start = time.time()

            extracted_fields = {}
            form_fields = []

            json_body = json.loads(request.body.decode("utf-8"))
            base64__ = json_body["data"]
            mode = json_body["mode"]
            invoice_type = json_body["type"]
            #print(base64__)
            if "config_name" in json_body:
                config_name=json_body["config_name"]
            else:
                config_name="global_config" 
            print("********")
            if not checkconfignameexists(config_name.split('|')[0]):
                config_name="global_config" 
            config,all_fields = get_extraction_list_from_db(config_name)
            config_name = config.name
            # Initialize lists to categorize the fields
            nearDistanceFieldsWithMasking = []
            nearDistanceFieldsWithoutMasking = []
            FlatFieldsWithMasking = []
            FlatFieldsWithoutMasking = []
            all_masked_fields = []

            # Loop through the fields once and categorize them
            for item in all_fields:
                algo_type = item.get('algo_type')
                masking = item.get('masking', False)  # Default to False if not present
                
                if algo_type == "NearestDistance":
                    if masking:
                        nearDistanceFieldsWithMasking.append(item)
                    else:
                        nearDistanceFieldsWithoutMasking.append(item)
                elif algo_type == "Horizontal":
                    if masking:
                        FlatFieldsWithMasking.append(item)
                    else:
                        FlatFieldsWithoutMasking.append(item)
                
                if masking:
                    all_masked_fields.append(item)

            #Finding the configuration 
  
            end = time.time()
            print("Time taken to read request body: ", end - start)

           
            # 'api' is not folder name in this case as it's a misnomer. This api not just extracts
            # list items, but full invoice details.
            start=time.time()
            request_id = log_to_db(request, "invoice_extraction", 'new_invoice_api', config_name)
            #config_name = config_name.split('|')[0].strip()
            end = time.time()
            print("Time taken to log to db: ", end - start)
            #checking if PDF FOLDER exists or not 
            #If PDF Folder does not exists , create it 

            if not os.path.isdir(BASE_DIR+'/Invoice/pdf'):
                os.mkdir(BASE_DIR+'/Invoice/pdf')
                print("pdf folder created...")
            
            if "extraction_id" in json_body.keys():
                unique_filename=json_body["extraction_id"]
            
            else:
                unique_filename = str(uuid.uuid4())
            
            #db_logger.info("Invoice Path "+invoice_path,{"user": str(request.user) ,"entity":unique_filename} )
            cl = CustomLogger(unique_filename)
            invoice_type=invoice_type.strip().lower()
            #PATH where main invoice is stored 
            if invoice_type == "pdf":
                invoice_path = base_path+unique_filename+".pdf"
            else:
                invoice_path = base_path+unique_filename+".png"
            start = time.time()
            #max_pagecount=get_max_pagecount(config_name) 
            max_pagecount = config.max_pagescan
            end=time.time()
            print("Time taken to get max page count: ", end - start)
            
            # Why this part written in this way
            # Can we simply written to create the file and if unable to simply return error
            try:
                #Saving the Invoice PDF/PNG
                decoded_data = base64.b64decode(base64__)
                with open(os.path.expanduser(invoice_path), 'wb') as fout:
                    fout.write(decoded_data)
                fout.close()
                try:
                    with Image.open(invoice_path) as img:
                        pass
                    
                except :
                    cl.print("Failed to create PNG file.. Trying to check file type from BASE64...")
                    bytesData = io.BytesIO()
                    bytesData.write(decoded_data)
                    bytesData.seek(0)  # Jump to the beginning of the file-like interface to read all content!
                    if("pdf" in (magic.from_buffer(bytesData.read()).lower())):
                        cl.print("Identified file type is PDF...")
                        if path.exists(invoice_path):
                            os.remove(invoice_path)
                        invoice_path = base_path+unique_filename+".pdf"
                        invoice_type = "pdf"
                        with open(os.path.expanduser(invoice_path), 'wb') as fout:
                            fout.write(base64.b64decode(base64__))
                        fout.close()
            except:
                cl.print("Unable to decode Base64...")

            images_list={} #Store only update maxpagecount as per config
            # images_list_700={} #Store all images
            # json_list={}
            # folder_list=[]
            # mapping_list=[]
            invoice_checklist={}


            images_count=0
            #convert the pdf into image 
            if invoice_type=="pdf":

                # indentified the size of page
                start = time.time()
                pageSizes = getPageSize(invoice_path, 3)
                end = time.time()
                print("Time taken to get page size: ", end - start)
                # interate through config of each page and generate image
                start = time.time()
                if len(pageSizes) > 0:
                    # images_700 = []
                    images = []
                    for i, size in enumerate(pageSizes):
                        # images_700.append(convert_from_path(invoice_path, first_page=i+1, last_page=i+1, size=size)[0])
                        images.append(convert_from_path(invoice_path, first_page=i+1, last_page=i+1, size=size)[0])
                else:
                    # images_700=convert_from_path(invoice_path,size=(772, 995))
                    # images_700=convert_from_path(invoice_path,size=(2000, None))
                    images = convert_from_path(invoice_path, size=(2000, None))
                
                start = time.time()
                for id,i in enumerate(images):
                    #break out if page count exceeds the dataabse configuration
                    if images_count >= max_pagecount:
                        cl.print("images count:"+str(images_count)+"max_pagecount :"+str(max_pagecount ))
                        break

                    i.save(base_path+unique_filename+"_"+str(id)+".png")
                    images_list[id]=base_path+unique_filename+"_"+str(id)+".png"
                    images_count=images_count+1

                # for id,i in enumerate(images_700):
                #     i.save(base_path+unique_filename+"_"+str(id)+"_700"+".png")
                #     images_list_700[id]=base_path+unique_filename+"_"+str(id)+"_700"+".png"

            else:
                start = time.time()
                i = Image.open(invoice_path)
                i.save(base_path+unique_filename+"_"+str(images_count)+".png")
                images_list[images_count]=base_path+unique_filename+"_"+str(images_count)+".png"
                # images_list_700[images_count]=base_path+unique_filename+"_"+str(images_count)+".png"
                images_count=1
                
                #print("exiting program....")
                #raise ValueError('A very specific bad thing happened.')

            # print("images list ",images_list)
            # print("images_list 700 ",images_list_700)
            image_keys=list(images_list.keys())
            image_keys.sort()
            start = time.time()
            update_page_count(request_id, images_count)
            end = time.time()
            print("Time taken to update page count: ", end - start)
            #Creating AMAZON Textract Instance 
            # Amazon Textract client
            # textract = boto3.client('textract','us-east-1')
            
            line_items_dict=[]
           
            AI_Fields={}
            Form_Present=False
            prediction_result={}
            companylist = config.company_names.strip().split(',')   
            for key in image_keys:
                inv_page_path=images_list[key]
                # inv_page_path_700=images_list_700[key]
                
                # We pass only page upto maxpagecount for this function
                if inv_page_path is not None and key not in prediction_result:
                    start = time.time()
                    pred_res=predictInvoice(TFmodel2,inv_page_path)
                    end = time.time()
                    print("Time taken to predict invoice: ", end - start)
                    prediction_result[key]=pred_res
                    
                
                isInvoice=True
                #if key>=0:
                #check if it is invoice or not
                # NI means Not Invoice I means Invoice
                next_image_path=None
                pred_next_im_class=None

                if key+1 in images_list:
                    next_image_path=images_list[key+1]

                    if next_image_path is not None:
                        if(images_count > 5):
                            start = time.time()
                            pred_next_im_class=predictInvoice(TFmodel2,next_image_path)
                            end = time.time()
                            print("Time taken to predict invoice: ", end - start)
                        else:
                            ##17/04/2024 HARDCODING PAGE AS INVOICE INCASE IF THE DOCUMENT IS <=5 PAGES.
                            pred_next_im_class={"NI":0,"I":1}
                        prediction_result[key+1] = pred_next_im_class
                    cl.print("Invoice -Invoice Ml Prediction ")
                    cl.print(" ##################################### "+str(key+1)+" ################### "+str(pred_next_im_class)) 

                        #check if NON-Invoice confidence is greater than equal to 70 
                    if pred_next_im_class["NI"]>=0.80:
                        invoice_checklist[key+1]="NI"
                        invoice_checklist["last_invoice"]=key
                        
                    else:
                        invoice_checklist[key+1]="I"
                        
                    #print("invoice checklist -- ",invoice_checklist)
                
                #json_path = base_path+unique_filename+'_'+str(key)+'invoice.json'
                # mapping_file=BASE_DIR+'/Invoice/pdf/'+unique_filename+'_'+str(key)+'mapping.json'
                
                #adding json lists :
                # json_list[key]=json_path
                # folder_list.append(json_path.replace('.json','-csv'))
                # mapping_list.append(mapping_file)
              
                
                if key in invoice_checklist:
                    if invoice_checklist[key]=="NI":
                        isInvoice=False
                
                #Skew Correction 
                #skew logic is bugged in some cases 
                #skewImage(invoice_path)

                # data_str = self.reader(inv_page_path_700)
                start=time.time()
                data_str = self.reader(inv_page_path)    
                end = time.time()
                print("Time taken to read image: ", end - start)
                # Perform OCR To invoice
                

                feature_field=""
                #Forms should only be fetched for First Invoice

                if key==0 or key==(len(images_list)-1) or key==(len(images_list)-2) :
                   
                    feature_field=['TABLES','FORMS']
                    Form_Present=True
                else:
                    if ("last_invoice" in invoice_checklist) and  (invoice_checklist["last_invoice"]==key):
                        feature_field=['TABLES','FORMS']
                        Form_Present=True
                    else:
                        feature_field=['TABLES']
                        Form_Present=False
                
                cl.print("Features Used "+str(feature_field))
                

                #################### PERFORMING OCR ##################
                if "extraction_id" not in json_body.keys() :
                    cl.print(message="fetching Boto Response .....")
                    start = time.time()
                    analyzed_page_data = client.analyze_document(
                        Document={'Bytes': data_str}, FeatureTypes=feature_field)
                    #with open(json_path, 'w') as fp:
                    #    json.dump(response, fp)
                    end = time.time()
                    print("Time taken to get boto response: ", end - start)
                    cl.print("JSON File Written ...")

                    #db_logger.info("OCR Successful",{"user": str(request.user) ,"entity":unique_filename} )

                #################### PERFORMING OCR ################## 

                #Extract AI Fields and merge into original fields 
                if Form_Present:
                    # cl.print("getting values for "+str(json_path))
                    start = time.time()
                    temp_dict=get_raw_values(analyzed_page_data)
                    AI_Fields.update(temp_dict)
                    end = time.time()
                    print("Time taken to get raw values: ", end - start)

                # CREATING Objects
                extract_util_obj = ExtractUtils()
                
                ######################################################
                ############## PYTORCH MODEL EXRACTION ##############
                op_image=None
                df=None
                cl.print("marking for "+str(inv_page_path))
                start = time.time()
                try:
                    op_image=markheader_main(analyzed_page_data,inv_page_path,inv_page_path.replace(".png","-markup.png"),config_name )
                except Exception as e:
                    cl.print("Marking Exception")
                    cl.print(str(e))
                end = time.time()
                print("Time taken to mark headers: ", end - start)
                if op_image is not None:
                    try:
                        start = time.time()
                        df=getlineitems(op_image,analyzed_page_data,config_name)
                        #cl.print("data frame lineitems ### ",len(df))
                        end = time.time()
                        print("Time taken to get line items: ", end - start)
                    except Exception as e:
                        #db_logger.info("Line Item Exception ### "+ str(e),{"user": str(request.user) ,"entity":unique_filename} )
                        cl.print("Line item Exception")
                        cl.print(str(e))

                table_dimensions=(0,0) 
                 # Finding Table Dimensions
                start = time.time()
                extract_util_obj.getHeight(inv_page_path)
                end = time.time()
                print("Time taken to get height: ", end - start)
                ########### EXTRACTING FIELDS START ###########################

                #Form Field will be only fetched for First Invocie

                if isInvoice:
                    #call formfields by Distance method
                    start = time.time()
                    form_fields=getFormFieldsbyDistance(config_name,form_fields,analyzed_page_data,nearDistanceFieldsWithoutMasking,table_dimensions,cl)
                    end = time.time()
                    print("Time taken to get form fields by distance: ", end - start)
                    #call form fields search by linear method
                    start = time.time()
                    form_fields,_,_ = getFormFieldsbyLinear(config_name,form_fields,analyzed_page_data,FlatFieldsWithoutMasking,cl)
                    end = time.time()
                    print("Time taken to get form fields by linear: ", end - start)
                ########### EXTRACTING FIELDS ENDS ###########################

                cl.print("extracted form fields ")
                start = time.time()
                confidence_dict=extract_util_obj.get_confidence_matrix(analyzed_page_data)
                end = time.time()
                print("Time taken to get confidence matrix: ", end - start)
                #db_logger.info("data frame ### "+ str(df),{"user": str(request.user) ,"entity":unique_filename} )
                
                start = time.time()
                nan_value = float("NaN") 
                temp_list=[]
                if df is not None:
                    if not df.empty:
                        #Remove empty string from dataframe
                        df=extract_util_obj.lineitemexclusions(config_name,df)
                        
                        df.replace(" ", nan_value, inplace=True)
                        df.replace("", nan_value, inplace=True)
                        df=df.dropna(how='all')
                        df=df.replace(nan_value, '', regex=True)
                        temp_list=extract_util_obj.df_to_dict(df,confidence_dict,config_name)
                        cl.print("Line Items added ",len(temp_list))
                        line_items_dict.append(temp_list) 
                end = time.time()
                print("Time taken to process line items: ", end - start)
        
                if (key>0):
                    if invoice_checklist[key]=="NI":
                        cl.print(str(key)+ " is NI")
                        # print("invoice checklist ")
                        # print(invoice_checklist)
                        break
            
            #combile all the line items into one list 
            #for maintaining the structure 
            # cl.print("line items dict "+str(line_items_dict))
            line_items_dict=[x for x in line_items_dict if x != []] #removing empty lists if any 

            
            if len(line_items_dict)==0:
                start = time.time()
                if not response_layer.executed:
                    response_layer.execute(images_list)
                
                # cl.print("structured line items "+str(response_layer.structured_lineitems))
                #db_logger.info("line items ### "+ str(response_layer.structured_lineitems),{"user": str(request.user) ,"entity":unique_filename}
                line_items_dict.append(response_layer.structured_lineitems) 
                end = time.time()
                print("Time taken to execute response layer: ", end - start)
            #line item counter
            combined_line_items=[]
            for i in line_items_dict:
                combined_line_items=combined_line_items+i
            
            ###### line item counter 
            li_counter=0
            for i in combined_line_items:
                i["fields"].append( {'id':li_counter} )
                li_counter=li_counter+1

            extracted_fields["line_items"]=combined_line_items
            
            raw_dict=AI_Fields
            
            # cl.print("AI Fields "+str(AI_Fields) )

            field_match=FieldMatch()
            start = time.time()
            end =time.time()
            print("Time taken to get extraction list: ", end - start)
            start = time.time()
            w_list=field_match.cerate_word_list(all_masked_fields)
            end = time.time()
            print("Time taken to create word list: ", end - start)
            start = time.time()
            match_threshold=get_ai_fieldmatch_threshold(config_name)
            # cl.print("match threshold "+str(match_threshold))
            end = time.time()
            print("Time taken to get match threshold: ", end - start)
            # Removing Keys having empty values
            for key in list(raw_dict):
                value=raw_dict[key]
                if value[0].strip()=="":
                    raw_dict.pop(key)
            # print("raw dict :",raw_dict)
            start = time.time()
            for key, value in raw_dict.items():
                  
                check_res=field_match.get_field_label(key,w_list,match_threshold) 
                
                key=key.replace(":","").replace(",","")
                search_key=None
                if check_res[1]==None:
                    data={key.strip():value[0].strip(),"confidence_level":value[1]}
                    search_key=key.strip()
                else:
                    data={check_res[1].strip():value[0].strip(),"confidence_level":value[1]}
                    search_key=check_res[1].strip()
                
                
                #check for any duplicate present in the list 
                for d in form_fields:
                    l = [item.lower().strip() for item in list(d.keys())]
                    if search_key.lower().strip() in l:
                        cl.print("Duplicate found "+str(d) + " :for "+ str(check_res[1]) +" , Match_score :")
                        form_fields.remove(d)
                
                form_fields.append(data)
            end = time.time()
            print("Time taken to append form fields: ", end - start)
            #print("checklist info ",prediction_result)
           
            ##############  Vendor Extraction Start ###############################
            #start=time.time()
            #companylist=get_companynames(config_name)
            #end = time.time()
            #print("Time taken to get company names: ", end - start)
            start = time.time()
            kv_vendorname,kv_score=filter_vendor_fields(form_fields,config_name,nlp) 
            end = time.time()
            print("Time taken to filter vendor fields: ", end - start)
            cl.print("Key Value Vendor name "+str(kv_vendorname))
            final_venname=None
            
            if kv_vendorname==None:
                # json_path_v=json_list[0]
                start = time.time()
                data_str_v = self.reader(images_list[0])   
                response = client.detect_document_text(
                        Document={'Bytes': data_str_v})
                # with open(json_path_v, 'w') as fp:
                #     json.dump(response, fp)
                end = time.time()
                print("Time taken to detect document text response: ", end - start)
                start = time.time()
                # print("jsn path vendor written ",json_path_v )
                # ven_res=getVendors(TF_OB_model,images_list[0],json_path_v,companylist,config_name,response_layer,images_list)
                #TODO: DISABLED TO VALIDATE LOGIC
                ven_res=getVendors(TF_OB_model,images_list[0],response,companylist,config_name,response_layer,images_list)
                #ven_res = [[]]
                #vendor_match=getVendorbycsvMatch(header_list)
                cl.print("ven res "+str(ven_res))
                ven_alt=None

                if ven_res[0] is None or len(ven_res[0])==0:
                    if not response_layer.executed:
                        # cl.print("images list vendor"+str(images_list))
                        ven_alt=response_layer.execute(images_list)
                    #ven_alt=response_layer.vendor_name
                    data={"vendor_name":ven_alt,"confidence_level":88 }
                    final_venname=ven_alt
                else:
                    data={"vendor_name":ven_res[0],"confidence_level":ven_res[1] }
                    final_venname=ven_res[0]


                
                if final_venname is not None:
                    form_fields.append(data)
            else:
                data={"vendor_name":kv_vendorname,"confidence_level":kv_score }
                final_venname=kv_vendorname
                form_fields.append(data)
            end = time.time()
            print("Time taken to get vendor name: ", end - start)
            ############## Vendor Extraction End ###############################

            # print("inv list ")
            # print(images_list)
            # print("inv list info")
            # print(invoice_list_info)

            # print("json")
            # print(form_fields) 
            #conditional Form Fields
            
            for key in image_keys:
                inv_page_path=images_list[key]
                temp_arr=inv_page_path.replace(".png","invoice.json")
                #last paramter True for hascondition_=True
                if path.exists(temp_arr):
                    start = time.time()
                    form_fields=getFormFieldsbyDistance(config_name,form_fields,temp_arr,nearDistanceFieldsWithMasking,table_dimensions,cl,hascondition_=True)
                    end = time.time()
                    print("Time taken to get form fields by distance(path.exists): "+temp_arr+" : ", end - start)
                    #call form fields search by linear method  
                    start = time.time()
                    form_fields,_,_=getFormFieldsbyLinear(config_name,form_fields,temp_arr,FlatFieldsWithMasking,cl,hascondition_=True)
                    end = time.time()
                    print("Time taken to get form fields by linear(path.exists): "+temp_arr+" : ", end - start)
            #adding conditional queries START :
            
            # print("prediction result  ",prediction_result)
            # print( "images_list_700 ")
            # print( images_list_700 )
            start = time.time()
            query_results=getQuery(config_name,form_fields,images_list,prediction_result) 
            end = time.time()
            print("Time taken to get query results: ", end - start)
            # print("query results ",query_results)
            for k,v in query_results.items(): 
            
                data={k:v,"confidence_level":91.1 }
                form_fields.append(data)
            
             #adding conditional queries END :
            start = time.time()
            #If Setting in Response 
            if image_in_response:
                extracted_fields["base64"]=base64__
            
            extracted_fields["extraction_id"]=unique_filename            
            extracted_fields["uid"]=unique_filename
            extracted_fields["form_fields"]=form_fields
            response_data={"extracted_data":extracted_fields}

            # Remove csv files extracted 
            
            if not mode=="test" : #"extraction_id" not in json_body.keys() :
                #removing folder
                
                #removing all json files 
                #if path.exists(json_path):
                #    os.remove(json_path)
                
                for item in images_list:
                    if path.exists(images_list[item]):
                        os.remove(images_list[item] )

                if path.exists(invoice_path):
                    os.remove(invoice_path)
                cl.print("all files cleaned ......")
           
            response_layer.executed=False
            response_layer.response=None
            response_layer=None
            del response_layer

            #save data to invoice counter
             
            #invoice_tracker_save(config_name,"invoice","POST",headers,scanned_pages,images_count,hostname,invoice_path,username,agent)
            update_status(request_id, 200)
            end = time.time()
            print("Time taken to update status: ", end - start)
            print("Time taken to complete api: ", end - request_start)
            
            return HttpResponse(json.dumps(response_data,indent=4, sort_keys=True, default=str)) 
        
        except ValueError as e:
            cl.print(traceback.format_exc())
            error={}
            error["error"]=str(e)
            cl.print("msg"+str(e))
            db_logger.exception("Line Item Exception",{"user": str(request.user) ,"entity":unique_filename})
            update_status(request_id, status=500, error_msg=str(e))
            return HttpResponse(json.dumps(error,indent=4, sort_keys=True, default=str))
        
        except Exception as e:
            print(traceback.format_exc())
            error={}
            error["error"]="Invalid JSON/Corruption JSON values"
            print("Error "+str(e))
            print("inv type: ["+invoice_type+"] config name: ["+config_name+"]",request.user,True)
            print("error "+ str(e)+ " invoicetype:"+invoice_type+", config_name:"+config_name,request.user,True,"exception")
            #db_logger.info(,{"user": str(request.user) ,"entity":unique_filename} )
            #db_logger.exception(, {"user": str(request.user) ,"entity":unique_filename})
            #db_logger.exception("Error",{"invoice_type": str(invoice_type) ,"config_name":config_name,"entity":unique_filename})
            update_status(request_id, status=500, error_msg=str(e))
            return HttpResponse(json.dumps(str(e),indent=4, sort_keys=True, default=str))
            
            