import cv2
import pandas as pd
import json
#import time  # Disabled as timing logs are no longer used
import gc
import torch
import numpy as np
from functools import lru_cache
import PIL.Image as Image

from detectron2 import model_zoo
from detectron2.engine import DefaultPredictor
from detectron2.config import get_cfg

from fuzzywuzzy import fuzz
from AMS.extract_settings import *
from Invoice.DbConfig import *



# Global caches for performance optimization
_config_cache = {}
_header_fields_cache = {}

@lru_cache(maxsize=128)
def get_cached_config(config_name):
    """Cache configuration lookups to avoid repeated DB queries."""
    if config_name not in _config_cache:
        res = Configuration.objects.filter(name=config_name)
        if res.exists():
            config = res.first()
            _config_cache[config_name] = {
                'min_col_in_line_items': config.min_col_in_line_items,
                'extract_field': config.extract_field
            }
        else:
            _config_cache[config_name] = {'min_col_in_line_items': 0, 'extract_field': None}
    return _config_cache[config_name]

@lru_cache(maxsize=128)
def get_cached_header_fields(config_name):
    """Cache header fields to avoid repeated DB queries."""
    if config_name not in _header_fields_cache:
        _header_fields_cache[config_name] = get_header_fields(config_name)
    return _header_fields_cache[config_name]

def clear_line_items_cache():
    """Clear caches to free memory."""
    global _config_cache, _header_fields_cache
    _config_cache.clear()
    _header_fields_cache.clear()
    get_cached_config.cache_clear()
    get_cached_header_fields.cache_clear()
    print("Line items caches cleared")

#########################Detectron loading model start ####################

py_path=None
if run_mode=='local':
    py_path='output/content/output/model_final.pth'

else:
    py_path='../output/content/output/model_final.pth'
    


cfg = get_cfg()
cfg.merge_from_file(model_zoo.get_config_file('COCO-Detection/faster_rcnn_R_50_FPN_3x.yaml'))
cfg.MODEL.ROI_HEADS.SCORE_THRESH_TEST = 0.3 # Set threshold for this model
cfg.MODEL.WEIGHTS = py_path # Set path model .pth
cfg.MODEL.ROI_HEADS.NUM_CLASSES = 3
cfg.MODEL.DEVICE="cpu"
predictor = DefaultPredictor(cfg)
print("pytorch model loaded ......")



################################checking overlap of cells and columns ############################

def overlap(rect1, rect2):
    """Optimized overlap detection using direct comparison instead of range()."""
    x1, x2 = int(rect1[0]), int(rect1[2])
    xx1, xx2 = int(rect2[0]), int(rect2[2])

    # Direct comparison is faster than range() for this use case
    return (x1 - 50 <= xx1 <= x2 + 50) and (x1 - 50 <= xx2 <= x2 + 50)


def overlapcells(rect1,rect2):
    #assuming rect2 smaller and rect1 larger 
    a1= (rect1[2]-rect1[0])*(rect1[3]-rect1[1])

    a2= (rect2[2]-rect2[0])*(rect2[3]-rect2[1])
    
    #rect2 is greater than 
    if not a1>a2:
      temp=rect1
      rect1=rect2
      rect2=temp
    
    x2=int(rect1[2])
    x1=int(rect1[0])
    y1=int(rect1[1])
    y2=int(rect1[3])

    xx2=int(rect2[2])
    xx1=int(rect2[0])
    yy1=int(rect2[1])
    yy2=int(rect2[3])
    
    if xx1 in range(x1,x2+10) and yy1 in range(y1,y2+10) :
      #its in the box
      return (True,a1,a2)
    else:
      return (False,a1,a2)  





###############################checking overlap of cells and columns ##############################


################################################getting content in the boxes :

def getTextFromBox(json_path,box,im_height,im_width):

    f = open(json_path,) 
    data = json.load(f)
    
    x1= box[0] -50
    x2= box[2] + 100
    y1= box[1] 
    y2= box[3]
    # print("get text from box ",y1,x1,y2,x2)
    text=[]
    for d in data["Blocks"]:
        if d["BlockType"]=="LINE":

            add=d["Geometry"]["Polygon"]
             
            y1_= (add[1]["Y"]*im_height)
            y2_= (add[3]["Y"]*im_height)
            x1_= (add[0]["X"]*im_width)
            x2_= (add[2]["X"]*im_width)
            
            #print(d["Text"],"--",y1_,x1_,y2_,x2_)
            
            if y1_>y1 and y2_<y2 and x1_>x1 and x2_<x2:
                # print("ex text ",d["Text"])
                text.append(d["Text"])
    print("text from box ",text)
    return text 



##################find horizontal lines by aws

def getrowrange(cell,finalrows,boxes):
    cx1,cy1,cx2,cy2,text=cell
    for row in finalrows:
        rx1,ry1,rx2,ry2=boxes[row].tolist()
        if int( ((cx1+cx2)/2) ) in range(int(rx1),int(rx2)):
            return row
    return None

def findlines_aws(json_path,y_min,y_max,width,height):
    f = open(json_path,) 
    data = json.load(f)

    t_cells=[]
    for d in data["Blocks"]:
        if d["BlockType"]=="LINE":

            add=d["Geometry"]["Polygon"]
            
            y1_= (add[1]["Y"]*height)
            y2_= (add[3]["Y"]*height)
            x1_= (add[0]["X"]*width)
            x2_= (add[2]["X"]*width)

            if int(y1_) in range( int(y_min),int(y_max) ):
                t_cells.append( [x1_,y1_,x2_,y2_,d['Text'] ] )
    return t_cells 


#####################Gets Rows if Table Detected 

def get_text(result, blocks_map):
    text = ''
    if 'Relationships' in result:
        for relationship in result['Relationships']:
            if relationship['Type'] == 'CHILD':
                for child_id in relationship['Ids']:
                    word = blocks_map[child_id]
                    if word['BlockType'] == 'WORD':
                        text += word['Text'] + ' '
                    if word['BlockType'] == 'SELECTION_ELEMENT':
                        if word['SelectionStatus'] =='SELECTED':
                            text +=  'X '    
    return text

def getlineitemsbyTables(data, config_name, y_min, y_max, width, height):
    """Optimized table processing with cached configuration."""
    # Use cached configuration
    config_data = get_cached_config(config_name)
    min_col_in_line_items = config_data['min_col_in_line_items']

    blocks = data.get('Blocks', [])
    if not blocks:
        return {}

    # Build blocks map and filter table blocks in one pass
    blocks_map = {}
    table_blocks = []
    for block in blocks:
        block_id = block['Id']
        blocks_map[block_id] = block
        if block['BlockType'] == "TABLE":
            table_blocks.append(block)

    if not table_blocks:
        return {}

    table_data = []
    li_rows = {}
    for d in data["Blocks"]:
        #print(d["BlockType"])
        if d["BlockType"]=="TABLE":
            relationships=d["Relationships"]
            
            ids=relationships[0]['Ids']

            for child_id in ids:
                cell = blocks_map[child_id]
                if cell['BlockType'] == 'CELL':
                    BoundingBox=cell["Geometry"]["BoundingBox"]
                    add=cell["Geometry"]["Polygon"]

                    y1_= (add[1]["Y"]*height)
                    y2_= (add[3]["Y"]*height)
                    x1_= (add[0]["X"]*width)
                    x2_= (add[2]["X"]*width)

                    row_index = cell['RowIndex']
                    col_index = cell['ColumnIndex']
                    if int(y1_) in range( int(y_min)-30,int(y_max) ):
                        if row_index not in li_rows:
                            # create new row
                            li_rows[row_index] = {}
                        # get the text value
                        li_rows[row_index][col_index] = get_text(cell, blocks_map)
            table_data.append(li_rows)
            li_rows = {}

    ##16th March 2024
    ##FILTERING TABLE BASED ON THEIR COLUMN COUNT.
    ## IF COLUMN COUNT IS LESS THAN CONFIGURED COUNT THEN REMOVE THE TABLE FROM table_data.
    filtered_table_data = []
    for table in table_data:
        row_nos = table.keys()
        for row_id  in row_nos:
            if len(list(table[row_id].keys())) >= min_col_in_line_items:
                filtered_table_data.append(table)
                break
    
    data = {}
    index = 0
    for table in filtered_table_data:
        for elem in table.keys():
            index+=1
            data[index] = table[elem] 

    return dict(sorted(data.items()))


# def getlineitemsbyTables_fallback(json_path):
#     f = open(json_path,) 
#     data = json.load(f)

#     blocks=data['Blocks']
    
#     blocks_map = {}
#     table_blocks = []
#     for block in blocks:
#         blocks_map[block['Id']] = block
#         if block['BlockType'] == "TABLE":
#             table_blocks.append(block)

#     li_rows = {}
#     for d in data["Blocks"]:
#         if d["BlockType"]=="TABLE":
#             relationships=d["Relationships"]
            
#             ids=relationships[0]['Ids']

#             for child_id in ids:
#                 cell = blocks_map[child_id]
#                 if cell['BlockType'] == 'CELL':
#                     row_index = cell['RowIndex']
#                     col_index = cell['ColumnIndex']
#                     li_rows[row_index][col_index] = get_text(cell, blocks_map)

#     return li_rows



def formcolscordsbycells_aws(t_cells,finalrows,boxes):
    cols_cords={}
    
    for cell2 in t_cells:
        #print(cell2)   
        c=cell2
        
        y=int(c[1])
        #y1=int(b[1])

        y_=int(c[3])
        #y1_=int(b[3])

        keyfound=False
        row=getrowrange(cell2,finalrows,boxes)
        # print('rows :',row)
        if row==None:
            continue
        for k in cols_cords.keys():
            
            if y in range(k[0]-20,k[1]+20) or y_ in range(k[0]-20,k[1]+20) :

                keyfound=True
                t=cols_cords[k]
                t.append( (cell2,row) )

                cols_cords[k]=t
            
        if keyfound==False:
            t=[]
            t.append( (cell2,row) )
            cols_cords[(y,y_)]=t
    return cols_cords

def getlineitemsbycells_aws(cols_cords):
    li_dict={}
    c=0
    for k,v in cols_cords.items():
        # print(v)
        sorted_l=sorted(v,key=lambda x: x[1])
        t={}
        for i in sorted_l:
            if i[1] in t:
                ts=t[i[1]]  
                ts=ts+" "+i[0][4]
                t[i[1]]=ts
            else:
                t[i[1]]=i[0][4]
        
        li_dict[c]=t
        c=c+1

    return li_dict


# header_items={"item name":'S',"sno":'S',"particular":'S',"description":'S',"rate":'M',"quantity":'S',"qty":'S',"total":'S',"amount":'M',"man day":'S',"hsn":'S',"price":'S',"uom":'S',"unit price":'M',"total":'S',"man days":'S',"grade":'S',"man month":'M',"sr.no":'S',"item":'S',"extended amount":'M',"net amount":'M',"part no":'S',"usage country":'S',"qty ordered":'S'}
# header_items_list=list(header_items.keys())

def checkislineitemheader(row, df, header_list):
    """Optimized header detection with early termination and caching."""
    finalrowcount = df.shape[1]

    # Determine threshold based on column count
    if finalrowcount >= 5:
        lowerrange = 4
    elif finalrowcount >= 3:
        lowerrange = 2
    else:
        lowerrange = finalrowcount

    match_count = 0

    # Pre-process header list for faster comparison
    header_list_lower = [str(item).lower().strip() for item in header_list if item]

    for col in row:
        if not col:
            continue

        col_lower = str(col).lower().strip()

        # Quick exact match check first
        if col_lower in header_list_lower:
            match_count += 1
            if match_count >= lowerrange:  # Early termination
                return True
            continue

        # Fuzzy matching only if exact match fails
        for item_lower in header_list_lower:
            if len(item_lower) < 3 or len(col_lower) < 3:
                continue

            ratio = fuzz.partial_ratio(item_lower, col_lower)
            if ratio > 80:
                match_count += 1
                if match_count >= lowerrange:  # Early termination
                    return True
                break

    return match_count >= lowerrange

# checks in data frame if header exists or not
def checkheaderexists(df, finalrows, header_list):
    """Optimized header existence check with early termination."""
    print("checking header exists")

    if df.empty or not header_list:
        return (False, 0)

    df_ = df.transpose()
    max_l = []

    # Process only first few rows for efficiency (headers are usually at the top)
    max_rows_to_check = min(5, df_.shape[0])

    for i in range(max_rows_to_check):
        row_values = df_.iloc[i].values

        # Skip empty rows
        if all(pd.isna(val) or str(val).strip() == '' for val in row_values):
            continue

        if checkislineitemheader(row_values, df_, header_list):
            max_l.append(i)

    if max_l:
        return (True, max(max_l))
    else:
        return (False, 0)
 

#########################Detectron loading model end ####################




def predictpy(img_path):
    im = cv2.imread(img_path)
    print("image_path ", img_path)

    with torch.no_grad():  # Ensure no gradients are tracked
        outputs = predictor(im)

    outputs["instances"] = outputs["instances"].to("cpu")

    if torch.cuda.is_available():
        torch.cuda.empty_cache()

    return outputs

def getlineitems(img_path, data, config_name):
    """Optimized line items extraction with performance monitoring."""
    #start_time = time.time()
    #print(f"Starting getlineitems at {time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(start_time))}")
    print("Starting line items extraction process")

    try:
        # Predict using PyTorch model
        #predict_start = time.time()
        #print(f"Starting ML prediction at {time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(predict_start))}")
        print("Starting ML prediction for table detection")
        outputs = predictpy(img_path)
        #predict_end = time.time()
        #print(f"Completed ML prediction in {predict_end - predict_start:.4f} seconds")
        print("ML prediction completed successfully")

        # Convert model outputs to plain Python objects early
        #process_start = time.time()
        #print(f"Starting output processing at {time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(process_start))}")
        print("Processing ML model outputs")

        instances = outputs["instances"]
        scores = instances.scores.cpu().numpy()
        boxes = instances.pred_boxes.tensor.cpu().numpy()
        classes = instances.pred_classes.cpu().numpy()

        # Vectorized classification - much faster than loop
        rows = np.where(classes == 0)[0].tolist()
        cells = np.where(classes == 1)[0].tolist()

        #process_end = time.time()
        #print(f"Completed output processing in {process_end - process_start:.4f} seconds")
        print(f"Detected {len(rows)} rows and {len(cells)} cells")

        # Optimized overlap detection with early termination
        #overlap_start = time.time()
        #print(f"Starting overlap detection at {time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(overlap_start))}")
        print("Processing row overlap detection")

        finalrows = rows[:]
        # rows_to_remove = set()

        # for j in rows:
        #     if j in rows_to_remove:
        #         continue
        #     rect2 = boxes[j]
        #     for i in rows:
        #         if i == j or i in rows_to_remove:
        #             continue
        #         rect1 = boxes[i]
        #         if overlap(rect1, rect2):
        #             if rect1[3] > rect2[3]:
        #                 rows_to_remove.add(j)
        #                 break
        #             else:
        #                 rows_to_remove.add(i)

        # # Remove overlapping rows efficiently
        # finalrows = [row for row in finalrows if row not in rows_to_remove]

        #overlap_end = time.time()
        #print(f"Completed overlap detection in {overlap_end - overlap_start:.4f} seconds")
        print(f"Overlap detection completed, final rows: {len(finalrows)}")

        # Optimized cell-row mapping
        #cell_mapping_start = time.time()
        #print(f"Starting cell mapping at {time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(cell_mapping_start))}")
        print("Mapping cells to rows")

        cell_d = {}
        for row in rows:
            r_x0, r_x1 = boxes[row][0], boxes[row][2]
            cell_d[row] = []

            for cell in cells:
                c_x0, c_x1 = boxes[cell][0], boxes[cell][2]
                if c_x0 >= (r_x0 - 10) and c_x1 <= (r_x1 + 10):
                    cell_d[row].append(cell)

        #cell_mapping_end = time.time()
        #print(f"Completed cell mapping in {cell_mapping_end - cell_mapping_start:.4f} seconds")
        print("Cell-to-row mapping completed")

        # Get image dimensions efficiently
        #image_start = time.time()
        #print(f"Starting image processing at {time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(image_start))}")
        print("Getting image dimensions")

        with Image.open(img_path) as im:
            width, height = im.size

        #image_end = time.time()
        #print(f"Completed image processing in {image_end - image_start:.4f} seconds")
        print(f"Image dimensions: {width}x{height}")

        if not finalrows:
            print("No final rows found, returning None")
            return None

        # Vectorized min/max calculation
        #bounds_start = time.time()
        #print(f"Starting bounds calculation at {time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(bounds_start))}")
        print("Calculating table boundaries")

        finalrows_boxes = boxes[finalrows]
        y_min, y_max = np.min(finalrows_boxes[:, 1]), np.max(finalrows_boxes[:, 3])

        #bounds_end = time.time()
        #print(f"Completed bounds calculation in {bounds_end - bounds_start:.4f} seconds")
        print(f"Table boundaries calculated: y_min={y_min:.2f}, y_max={y_max:.2f}")

        # Extract line items from tables
        #table_start = time.time()
        #print(f"Starting table extraction at {time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(table_start))}")
        print("Extracting line items from detected tables")

        li_rows = getlineitemsbyTables(data, config_name, y_min, y_max, width, height)

        #table_end = time.time()
        #print(f"Completed table extraction in {table_end - table_start:.4f} seconds")
        print("Table extraction completed")

        if not li_rows:
            print("No line items found in tables")
            return None

        df = pd.DataFrame(li_rows)
        print(f"Created DataFrame with {df.shape[0]} rows and {df.shape[1]} columns")

        # Header detection and processing
        #header_start = time.time()
        #print(f"Starting header detection at {time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(header_start))}")
        print("Starting header detection process")

        header_list = get_cached_header_fields(config_name)
        header_exists, header_index = checkheaderexists(df, finalrows, header_list)

        #header_end = time.time()
        #print(f"Completed header detection in {header_end - header_start:.4f} seconds")
        print(f"Header detection completed - Header exists: {header_exists}")

        if header_exists:
            df = df.transpose()
            header = df.iloc[header_index]
            new_df = pd.DataFrame(df.values[header_index+1:], columns=header)
            #print("HEADER ROW:\n", header)
            #print("SHAPE BEFORE:", df.shape)
            print(f"Final DataFrame shape: {new_df.shape[0]} rows, {new_df.shape[1]} columns")

            #total_end = time.time()
            #print(f"Total getlineitems execution time: {total_end - start_time:.4f} seconds")
            print("Line items extraction completed successfully")
            return new_df

    except Exception as e:
        print(f"Error in getlineitems: {str(e)}")
        return None
    finally:
        # Explicit cleanup
        try:
            del outputs, instances, scores, boxes, classes, rows, cells, finalrows, cell_d
            if 'df' in locals():
                del df
            if 'li_rows' in locals():
                del li_rows
            gc.collect()
            if torch.cuda.is_available():
                torch.cuda.empty_cache()
            print("Memory cleanup completed")
        except:
            pass

    #total_end = time.time()
    #print(f"Total getlineitems execution time: {total_end - start_time:.4f} seconds")
    print("Line items extraction process completed")
    return None